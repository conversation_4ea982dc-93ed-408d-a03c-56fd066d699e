class UserDealershipsController < AdminController
  before_action :set_user
  before_action :set_user_dealership, only: [ :destroy ]

  def create
    @user_dealership = @user.user_dealerships.build(user_dealership_params)

    if @user_dealership.save
      redirect_to user_path(@user), notice: "Dealership association created successfully."
    else
      redirect_to user_path(@user), alert: "Failed to create dealership association: #{@user_dealership.errors.full_messages.join(', ')}"
    end
  end

  def destroy
    @user_dealership.destroy!
    redirect_to user_path(@user), notice: "Dealership association removed successfully."
  end

  private

  def set_user
    @user = User.find_by(uuid: params[:user_id])
  end

  def set_user_dealership
    @user_dealership = @user.user_dealerships.find(params[:id])
  end

  def user_dealership_params
    params.expect(user_dealership: [ :dealership_id, :role ])
  end
end
