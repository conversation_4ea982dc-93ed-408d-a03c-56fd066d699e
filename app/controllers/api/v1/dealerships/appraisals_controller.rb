class Api::V1::Dealerships::AppraisalsController < Api::V1::BaseController
  include DateTimeFormatHelper
  before_action :set_appraisal, except: [ :index, :create, :dashboard ]
  before_action :ensure_appraisal_not_archived_or_deleted!, except: [ :index, :show, :create, :destroy, :dashboard, :archive ]
  before_action :set_customer_vehicle, except: [ :index, :create, :destroy, :show, :create_vehicle, :attach_customer_signature, :reassign, :favourite, :update, :archive, :dashboard, :send_for_offers, :update_customer ]
  before_action :set_vehicle_condition, only: [ :upsert_body_part_condition, :upsert_component_rating, :bulk_upsert_reconditioning_costs ]

  def index
    appraisals = filtered_appraisals
    @pagy, @appraisals = pagy(appraisals, limit: pagination_validated_per_page, page: params[:page] || 1)
    set_pagination_headers(@pagy)
    render :index
  end

  def show
    render :show
  end

  def create
    validate_create_params!

    @appraisal = dealership.appraisals.new(
      customer:,
      sales_person: current_user,
      created_by: current_user,
      updated_by: current_user,
      status: :incomplete
    )

    @appraisal.save!
    @status_code = 201
    @status_message = "Appraisal created successfully"
    render :show, status: :created
  end

  def update
    validate_update_params!

    update_attrs = update_params.slice(
      :projected_arrival_date,
      :appraisal_status,
      :awarded_value,
      :price,
      :given_price,
      :awarded_notes,
      :notes
    ).compact_blank.merge(updated_by_id: current_user.id)

    if update_attrs[:given_price].present? && update_attrs[:given_price].to_f == 0
      update_attrs[:given_price] = nil
    end

    @appraisal.update!(update_attrs)
    @status_message = "Appraisal updated successfully"
    render :show
  end

  def destroy
    @appraisal.update!(
      status: :deleted,
      updated_by: current_user
    )

    render_success_response("Appraisal deleted successfully")
  end

  def archive
    validate_archive_params!

    archive_status = ActiveModel::Type::Boolean.new.cast(archive_params[:status])

    if archive_status
      # Archive the appraisal
      if @appraisal.archived?
        raise Errors::InvalidInput, "Appraisal is already archived"
      end

      @appraisal.update!(
        previous_status: @appraisal.status,
        status: :archived,
        updated_by: current_user
      )
      @status_message = "Appraisal archived successfully"
    else
      # Unarchive the appraisal
      unless @appraisal.archived?
        raise Errors::InvalidInput, "Appraisal is not archived"
      end

      if @appraisal.previous_status.blank?
        raise Errors::InvalidInput, "Cannot unarchive: previous status not found"
      end

      @appraisal.update!(
        status: @appraisal.previous_status,
        previous_status: nil,
        updated_by: current_user
      )
      @status_message = "Appraisal unarchived successfully"
    end

    render :show
  end

  def attach_customer_signature
    @appraisal.attach_customer_signature(customer_signature_params[:signature])

    if @appraisal.valid?
      @status_message = "Customer signature added successfully"
      render :show
    else
      raise Errors::InvalidInput, @appraisal.errors.full_messages.join(", ")
    end
  end

  def create_vehicle
    validate_create_vehicle_params!

    customer_vehicle = @appraisal.build_customer_vehicle(vehicle_params.except(:brand_uuid))
    customer_vehicle.customer = @appraisal.customer
    customer_vehicle.dealership = @appraisal.dealership
    customer_vehicle.brand = find_brand(vehicle_params[:brand_uuid]) if vehicle_params[:brand_uuid].present?

    customer_vehicle.save!
    @appraisal = @appraisal.reload
    @status_code = 201
    @status_message = "Vehicle created successfully"
    render :show, status: :created
  end

  def update_vehicle
    # Handle brand_uuid separately - can be cleared
    if vehicle_params.has_key?(:brand_uuid)
      @customer_vehicle.brand = vehicle_params[:brand_uuid].present? ? find_brand(vehicle_params[:brand_uuid]) : nil
    end

    @customer_vehicle.update!(vehicle_params.except(:brand_uuid))
    @appraisal = @appraisal.reload
    @status_message = "Vehicle updated successfully"
    render :show
  end

  def update_customer
    validate_update_customer_params!

    @appraisal.update!(customer: customer)

    if @appraisal.customer_vehicle.present?
      @appraisal.customer_vehicle.update!(customer: @appraisal.customer)
    end

    @appraisal = @appraisal.reload
    @status_message = "Customer updated successfully"
    render :show
  end

  def upsert_vehicle_condition
    vehicle_condition = @customer_vehicle.vehicle_condition || @customer_vehicle.build_vehicle_condition
    vehicle_condition.assign_attributes(condition_params)

    # Store the new record state BEFORE saving
    was_new_record = vehicle_condition.new_record?
    vehicle_condition.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Vehicle condition #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def upsert_body_part_condition
    body_part_condition = @vehicle_condition.body_part_conditions.find_or_initialize_by(
      part_name: body_part_condition_params[:part_name]
    )

    body_part_condition.assign_attributes(body_part_condition_params)

    # Store the new record state BEFORE saving
    was_new_record = body_part_condition.new_record?
    body_part_condition.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Body part condition #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def upsert_component_rating
    component_rating = @vehicle_condition.component_ratings.find_or_initialize_by(
      name: component_rating_params[:name]
    )

    component_rating.assign_attributes(component_rating_params)

    # Store the new record state BEFORE saving
    was_new_record = component_rating.new_record?
    component_rating.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Component rating #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def bulk_upsert_reconditioning_costs
    reconditioning_costs_params[:reconditioning_costs].each do |cost_params|
      reconditioning_cost = @vehicle_condition.reconditioning_costs.find_or_initialize_by(
        cost_type: cost_params[:cost_type]
      )
      reconditioning_cost.assign_attributes(cost_params)
      reconditioning_cost.save!
    end

    @appraisal = @appraisal.reload
    @status_message = "Reconditioning costs updated successfully"
    render :show
  end

  def favourite
    validate_favourite_params!

    status = ActiveModel::Type::Boolean.new.cast(favourite_params[:status])

    if status
      # Add to favourites if not already exists
      unless @appraisal.favourited_by?(current_user)
        @appraisal.favourite_appraisals.create!(user: current_user)
      end
      message = "Appraisal added to favourites"
    else
      # Remove from favourites if exists
      favourite_record = @appraisal.favourite_appraisals.find_by(user: current_user)
      favourite_record&.destroy!
      message = "Appraisal removed from favourites"
    end

    @appraisal = @appraisal.reload
    @status_message = message
    render :show
  end

  def upsert_finance_details
    finance_details = @customer_vehicle.finance_details || @customer_vehicle.build_finance_details
    finance_details.assign_attributes(finance_params)
    # Store the new record state BEFORE saving
    was_new_record = finance_details.new_record?
    finance_details.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Finance details #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def upsert_options_fitted
    options_fitted = @customer_vehicle.options_fitted || @customer_vehicle.build_options_fitted
    options_fitted.assign_attributes(options_fitted_params)

    # Store the new record state BEFORE saving
    was_new_record = options_fitted.new_record?
    options_fitted.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Options fitted #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def upsert_vehicle_history
    vehicle_history = @customer_vehicle.vehicle_history || @customer_vehicle.build_vehicle_history
    vehicle_history.assign_attributes(vehicle_history_params)

    # Store the new record state BEFORE saving
    was_new_record = vehicle_history.new_record?
    vehicle_history.save!

    @appraisal = @appraisal.reload
    @status_code = was_new_record ? 201 : 200
    @status_message = "Vehicle history #{was_new_record ? 'created' : 'updated'} successfully"
    render :show, status: was_new_record ? :created : :ok
  end

  def reassign
    validate_reassign_params!

    new_sales_person = find_and_validate_user(reassign_params[:sales_person_uuid])

    @appraisal.update!(
      sales_person: new_sales_person,
      updated_by: current_user
    )

    @status_message = "Appraisal reassigned successfully"
    render :show
  end

  def dashboard
    dashboard_data = {
      total_appraisals: dealership.appraisals.count,
      completed: dealership.appraisals.complete.count,
      pending: dealership.appraisals.incomplete.count,
      awarded: dealership.appraisals.awarded.count,
      archived: dealership.appraisals.archived.count,
      my_favourites: dealership.appraisals.favourited_by_user(current_user).count
    }

    render_success_response("Dashboard data retrieved successfully", dashboard_data)
  end

  def send_for_offers
    raise Errors::InvalidInput, "Appraisal is not ready for offers" unless @appraisal.ready_for_offers?

    validate_send_for_offers_params!

    offers_data = send_for_offers_params[:offers]

    # Step 1: Validate add_valuer email uniqueness
    offers_data.each do |offer_data|
      if offer_data[:add_valuer] == true && offer_data[:valuer_email].present?
        existing_valuer = dealership.appraisal_valuers.find_by(email: offer_data[:valuer_email])
        if existing_valuer.present?
          raise Errors::InvalidInput, "Valuer with email #{offer_data[:valuer_email]} already exists for this dealership"
        end
      end
    end

    # Step 2: Process each offer

    ActiveRecord::Base.transaction do
      offers_data.each do |offer_data|
        process_single_offer(offer_data)
      end

      # Step 3: Update appraisal status
      @appraisal.update!(
        status: :complete,
        updated_by: current_user
      ) unless @appraisal.complete?
    end

    @appraisal = @appraisal.reload

    @status_message = "Offers sent successfully"
    render :show
  end

  private

  def process_single_offer(offer_data)
    offer_attributes = {
      appraisal: @appraisal,
      offer_price: offer_data[:offer_price],
      offer_notes: offer_data[:offer_notes],
      offer_date: Date.current
    }

    if offer_data[:is_internal] == true
      # Internal offer
      offer_attributes.merge!(
        is_verbal_offer: false,
        is_internal_offer: true,
        verbal_offer_by_id: current_user.id
      )
    elsif offer_data[:appraisal_valuer_uuid].present?
      # Existing valuer offer
      valuer = dealership.appraisal_valuers.active.find_by(uuid: offer_data[:appraisal_valuer_uuid])
      offer_attributes.merge!(
        appraisal_valuer: valuer,
        is_verbal_offer: offer_data[:is_verbal] == true
      )

      if offer_data[:is_verbal] == true
        offer_attributes[:verbal_offer_by_id] = current_user.id
      else
        offer_attributes[:offer_price] = nil
        offer_attributes[:offer_notes] = nil
      end
    elsif offer_data[:add_valuer] == true
      # Create new valuer and offer

      new_valuer = dealership.appraisal_valuers.create!(
        business_name: offer_data[:valuer_business_name],
        email: offer_data[:valuer_email],
        first_name: offer_data[:valuer_first_name],
        last_name: offer_data[:valuer_last_name],
        mobile_number: offer_data[:valuer_mobile_number]
      )

      offer_attributes.merge!(
        appraisal_valuer: new_valuer,
        is_verbal_offer: true,
        verbal_offer_by_id: current_user.id
      )
    else
      # External valuer offer (no valuer record)

      offer_attributes.merge!(
        valuer_business_name: offer_data[:valuer_business_name],
        valuer_email: offer_data[:valuer_email],
        valuer_first_name: offer_data[:valuer_first_name],
        valuer_last_name: offer_data[:valuer_last_name],
        valuer_mobile_number: offer_data[:valuer_mobile_number],
        is_verbal_offer: true,
        verbal_offer_by_id: current_user.id
      )
    end

    AppraisalOffer.create!(offer_attributes)
  end

  def validate_verbal_offer_price!(offer_price)
    if offer_price.blank? || offer_price.to_f <= 0
      raise Errors::InvalidInput, "offer_price is mandatory for verbal offers and must be greater than 0"
    end
  end

  def set_appraisal
    @appraisal = dealership.appraisals
                          .includes(:customer, :sales_person, :created_by, :updated_by,
                                   { customer_vehicle: :brand })
                          .where.not(status: :deleted)
                          .find_by!(uuid: params[:appraisal_uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Appraisal not found or does not belong to this dealership"
  end

  def set_customer_vehicle
    @customer_vehicle = @appraisal.customer_vehicle
    ensure_customer_vehicle_exists!
  end

  def set_vehicle_condition
    @vehicle_condition = @customer_vehicle.vehicle_condition
    validate_vehicle_condition_exists!
  end

  def filtered_appraisals
    if index_params[:status].present? && index_params[:status] == "deleted"
      appraisals = dealership.appraisals.unscoped.includes(:customer, :sales_person, :created_by, :updated_by, customer_vehicle: :brand)
    else
      appraisals = dealership.appraisals.includes(:customer, :sales_person, :created_by, :updated_by, customer_vehicle: :brand)
    end

    appraisals = appraisals.filter_by_status(index_params[:status]) if index_params[:status].present?
    appraisals = appraisals.by_salesperson(current_user.uuid) if index_params[:only_mine].present?
    appraisals = appraisals.favourited_by_user(current_user) if index_params[:favourites].present?
    appraisals = appraisals.by_customer(index_params[:customer_uuid]) if index_params[:customer_uuid].present?
    appraisals = appraisals.by_salesperson(index_params[:salesperson_uuid]) if index_params[:salesperson_uuid].present?
    appraisals = appraisals.by_brand(index_params[:brand_uuid]) if index_params[:brand_uuid].present?
    appraisals = appraisals.by_registration_number(index_params[:registration_number]) if index_params[:registration_number].present?
    appraisals = appraisals.created_between_dates(index_params[:start_date], index_params[:end_date]) if date_filters_present?
    appraisals = appraisals.search_by_term(index_params[:query]) if index_params[:query].present?

    appraisals.order(updated_at: :desc)
  end

  def date_filters_present?
    index_params[:start_date].present? || index_params[:end_date].present?
  end

  def index_params
    params.permit(:status, :only_mine, :query, :brand_uuid, :registration_number,
                  :salesperson_uuid, :start_date, :end_date, :customer_uuid, :favourites)
  end

  def validate_create_params!
    raise Errors::InvalidInput, "customer_uuid is required" if permitted_params[:customer_uuid].blank?
  end

  def validate_create_vehicle_params!
    if @appraisal.customer_vehicle.present?
      raise Errors::InvalidInput, "Appraisal already has a vehicle associated with it"
    end
  end

  def validate_update_customer_params!
    raise Errors::InvalidInput, "customer_uuid is required" if permitted_params[:customer_uuid].blank?
  end

  def validate_reassign_params!
    if reassign_params[:sales_person_uuid].blank?
      raise Errors::InvalidInput, "sales_person_uuid is required"
    end
  end

  def find_and_validate_user(user_uuid)
    dealership.users.find_by!(uuid: user_uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "User not found or does not belong to this dealership"
  end

  def ensure_appraisal_not_archived_or_deleted!
    if @appraisal.archived? || @appraisal.deleted?
      raise Errors::InvalidInput, "Cannot modify #{@appraisal.status} appraisal"
    end
  end

  def ensure_customer_vehicle_exists!
    if @appraisal.customer_vehicle.blank?
      raise Errors::InvalidInput, "No vehicle found for this appraisal"
    end
  end

  def validate_vehicle_condition_exists!
    if @customer_vehicle.vehicle_condition.blank?
      raise Errors::InvalidInput, "No vehicle condition found for this appraisal"
    end
  end

  def validate_favourite_params!
    unless favourite_params.has_key?(:status)
      raise Errors::InvalidInput, "status parameter is required"
    end

    unless [ "true", "false", true, false ].include?(favourite_params[:status])
      raise Errors::InvalidInput, "status must be true or false"
    end
  end

  def validate_archive_params!
    unless archive_params.has_key?(:status)
      raise Errors::InvalidInput, "status parameter is required"
    end

    unless [ "true", "false", true, false ].include?(archive_params[:status])
      raise Errors::InvalidInput, "status must be true or false"
    end
  end

  def validate_send_for_offers_params!
    if send_for_offers_params[:offers].blank?
      raise Errors::InvalidInput, "offers array is required"
    end

    unless send_for_offers_params[:offers].is_a?(Array)
      raise Errors::InvalidInput, "offers must be an array"
    end

    if send_for_offers_params[:offers].empty?
      raise Errors::InvalidInput, "offers array cannot be empty"
    end

    send_for_offers_params[:offers].each_with_index do |offer, index|
      validate_single_offer_params!(offer, index)
    end
  end

  def validate_single_offer_params!(offer, index)
    # Validate boolean fields
    if offer[:is_internal].present?
      unless [ true, false ].include?(offer[:is_internal])
        raise Errors::InvalidInput, "is_internal must be true or false for offer #{index + 1}"
      end
    end

    if offer[:is_verbal].present?
      unless [ true, false ].include?(offer[:is_verbal])
        raise Errors::InvalidInput, "is_verbal must be true or false for offer #{index + 1}"
      end
    end

    if offer[:add_valuer].present?
      unless [ true, false ].include?(offer[:add_valuer])
        raise Errors::InvalidInput, "add_valuer must be true or false for offer #{index + 1}"
      end
    end

    # Validate required fields based on conditions
    if offer[:is_internal] == false && offer[:appraisal_valuer_uuid].blank?
      required_fields = [ :valuer_email, :valuer_first_name, :valuer_last_name, :valuer_mobile_number ]
      required_fields.each do |field|
        if offer[field].blank?
          raise Errors::InvalidInput, "#{field} is required for external valuer offers (offer #{index + 1})"
        end
      end
    end

    if offer[:is_internal] == true || offer[:appraisal_valuer_uuid].blank? || offer[:is_verbal] == true || offer[:add_valuer] == true
      validate_verbal_offer_price!(offer[:offer_price])
    end
  end

  def vehicle_params
    params.expect(
      vehicle: [ :brand_uuid, :make, :model, :vin, :rego, :registration_expiry, :registration_state,
               :build_year, :build_month, :compliance_year, :compliance_month,
               :exterior_color, :interior_color, :seat_type, :fuel_type, :driving_wheels,
               :spare_wheel_type, :transmission, :body_type, :number_of_doors, :number_of_seats,
               :engine_kilowatts, :engine_number, :engine_size, :wheel_size, :wheel_size_front, :wheel_size_rear, :odometer_reading, :odometer_date,
               :is_vehicle_present, :redbook_code, :main_photo, :odometer_reading_photo, photos: [] ])
  end

  def condition_params
    params.expect(
      condition: [ :is_clean, :is_wet, :is_road_tested, :has_signs_of_repair, :repair_details,
                  :additional_notes, photos: [] ]
    )
  end

  def body_part_condition_params
    params.expect(
      body_part_condition: [ :part_name, :condition, :description, photos: [] ]
    )
  end

  def component_rating_params
    params.expect(
      component_rating: [ :name, :rating, photos: [] ]
    )
  end

  def reconditioning_costs_params
    params.permit(
      reconditioning_costs: [ :cost_type, :amount, :currency ]
    )
  end


  def validate_update_params!
    if update_params.empty? || update_params.values.all?(&:blank?)
      raise Errors::InvalidInput, "At least one non-blank field must be provided for update"
    end
  end

  def update_params
    params.permit(:projected_arrival_date, :appraisal_status,
                  :awarded_value, :price, :given_price, :awarded_notes, :notes)
  end

  def customer_signature_params
    params.permit(:signature)
  end

  def finance_params
    params.expect(
      finance: [
        :is_financed,
        :finance_company,
        :current_repayment_amount,
        :terms_months,
        :interest_rate,
        :next_due_date,
        :has_clear_title,
        :payout_amount
      ]
    )
  end

  def vehicle_history_params
    params.expect(
      vehicle_history: [
        :number_of_owners,
        :has_accident_history,
        :accident_details,
        :last_service_date,
        :last_service_odometer,
        :next_service_due,
        :has_dash_warning_lights,
        :dash_warning_details,
        :notes,
        :vehicle_history_status,
        service_book_images: []
      ]
    )
  end

  def options_fitted_params
    params.expect(
      options_fitted: [
        :has_sunroof,
        :has_tinted_windows,
        :has_towbar,
        :has_keyless_entry,
        :has_bluetooth,
        :has_ventilated_seats,
        :has_tray_fitted,
        :has_canopy_fitted,
        :has_aftermarket_wheels,
        :has_bull_bar,
        :has_extended_warranty,
        :extended_warranty_expiry,
        :ppsr,
        :sunroof_type,
        :number_of_keys,
        :heated_seats,
        :cargo_blind,
        :tonneau_cover,
        :tonneau_type,
        :on_written_off_register,
        :last_ppsr_date,
        :notes,
        additional_options: {},
        options_images: []
      ]
    )
  end

  def reassign_params
    params.permit(:sales_person_uuid)
  end

  def permitted_params
    params.permit(:customer_uuid)
  end

  def favourite_params
    params.permit(:status)
  end

  def archive_params
    params.permit(:status)
  end

  def send_for_offers_params
    params.permit(
      offers: [
        :offer_price, :offer_notes, :is_internal, :appraisal_valuer_uuid, :is_verbal,
        :valuer_business_name, :valuer_email, :valuer_first_name, :valuer_last_name,
        :valuer_mobile_number, :add_valuer
      ]
    )
  end
end
