class DeviceRegistrationsController < AdminController
  before_action :set_user
  before_action :set_device_registration, only: [ :destroy ]

  def destroy
    @device_registration.invalidate!
    redirect_to user_path(@user), notice: "Device registration deleted successfully."
  end

  private

  def set_user
    @user = User.find_by(uuid: params[:user_id])
  end

  def set_device_registration
    @device_registration = @user.device_registrations.find_by(id: params[:id])
    redirect_to user_path(@user), alert: "Device registration not found." unless @device_registration
  end
end
