class AppraisalOffer < ApplicationRecord
  belongs_to :appraisal_valuer, optional: true
  belongs_to :appraisal

  validates :offer_price, numericality: { greater_than: 0 }
  validates :offer_notes, length: { maximum: 1000 }, allow_blank: true

  validates :valuer_business_name, length: { maximum: 255 }
  validates :valuer_email, email_format: true, length: { maximum: 255 }
  validates :valuer_first_name, length: { maximum: 100 }
  validates :valuer_last_name, length: { maximum: 100 }
  validates :valuer_mobile_number, length: { maximum: 20 }

  validates :valuer_first_name, :valuer_last_name, :valuer_email, :valuer_mobile_number, presence: true, unless: :appraisal_valuer_id?
end
