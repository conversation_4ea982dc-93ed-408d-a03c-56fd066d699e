class AppraisalOffer < ApplicationRecord
  include HasUuid

  belongs_to :appraisal_valuer, optional: true
  belongs_to :verbal_offer_by, class_name: "User", optional: true
  belongs_to :appraisal

  validates :offer_price, numericality: { greater_than: 0 }, allow_nil: true
  validates :offer_notes, length: { maximum: 1000 }, allow_blank: true

  validates :valuer_business_name, length: { maximum: 255 }
  validates :valuer_email, email_format: true, length: { maximum: 255 }
  validates :valuer_first_name, length: { maximum: 100 }
  validates :valuer_last_name, length: { maximum: 100 }
  validates :valuer_mobile_number, length: { maximum: 20 }

  validates :valuer_first_name, :valuer_last_name, :valuer_email, :valuer_mobile_number, presence: true, unless: :skip_valuer_details_validation?

  private

  def skip_valuer_details_validation?
    appraisal_valuer_id? || is_internal_offer?
  end
end
