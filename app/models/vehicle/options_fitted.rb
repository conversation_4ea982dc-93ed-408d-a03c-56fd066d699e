module Vehicle
  class OptionsFitted < ApplicationRecord
    include HasUuid

    # Associations
    belongs_to :customer_vehicle

    # Enums
    enum :sunroof_type, { standard_metal: 0, standard_glass: 1, panoramic: 2 }, prefix: true
    enum :tonneau_type, { hard: 0, soft: 1 }, prefix: true
    enum :on_written_off_register, { yes: 0, no: 1, unknown: 2 }, prefix: true

    # Validations
    validates :number_of_keys, inclusion: { in: [ 1, 2, 3 ] }, allow_nil: true
    validates :extended_warranty_expiry, presence: true, if: :has_extended_warranty?
    validates :notes, length: { maximum: 1000 }, allow_blank: true

    # Scopes
    scope :with_extended_warranty, -> { where(has_extended_warranty: true) }
    scope :with_ppsr, -> { where(ppsr: true) }

    # Attachments
    has_many_attached :options_images
    validates :options_images, content_type: [ "image/png", "image/jpeg" ],
                              size: { less_than: 5.megabytes },
                              limit: { max: 5 }

    # Override setters to clear dependent fields when boolean is set to false
    def has_sunroof=(value)
      super(value)
      self.sunroof_type = nil unless has_sunroof?
    end

    def tonneau_cover=(value)
      super(value)
      self.tonneau_type = nil unless tonneau_cover?
    end

    def has_extended_warranty=(value)
      super(value)
      self.extended_warranty_expiry = nil unless has_extended_warranty?
    end

    def ppsr=(value)
      super(value)
      self.last_ppsr_date = nil unless ppsr?
    end
  end
end
