class DealershipVehicle < ApplicationRecord
  include VehicleBase

  has_many :drives, dependent: :destroy, foreign_key: "vehicle_id", inverse_of: :vehicle
  has_many :damage_reports, foreign_key: "vehicle_id", inverse_of: :vehicle, dependent: :destroy
  has_one :last_damage_report, -> { where(report_type: DamageReport::VEHICLE) }, class_name: "DamageReport",
                                   dependent: :destroy, foreign_key: "vehicle_id", inverse_of: :vehicle
  has_one :last_known_location, as: :trackable, class_name: "GpsLocation", dependent: :destroy

  validates :build_year, presence: true

  enum :status, {
    available: 0,
    in_use: 1,
    out_of_service: 2,
    deleted: 3,
    sold: 4,
    enquiry: 5
  }, default: :available

  enum :vehicle_type, {
    new_vehicle: 0,
    demo: 1,
    old: 2
  }

  scope :available_for_test_drive, -> { where(status: :available) }
  scope :not_deleted, -> { where.not(status: :deleted) }

  # Search scope similar to Customer.search_by_term
  scope :search_by_term, ->(term, limit: 20) {
    return none if term.blank?

    # Reject if less than 3 characters or more than 20 characters
    cleaned_term = term.strip
    return none if cleaned_term.length < 3 || cleaned_term.length > 20

    search_term = "%#{cleaned_term.downcase}%"
    where(
      "LOWER(stock_number) LIKE :term OR
       LOWER(rego) LIKE :term OR
       LOWER(make) LIKE :term OR
       LOWER(model) LIKE :term OR
       LOWER(color) LIKE :term OR
       CAST(build_year as CHAR) LIKE :term",
      term: search_term
    ).ordered_by_name.limit(limit)
  }

  def currently_on_test_drive?
    drives.active.exists?
  end
end
