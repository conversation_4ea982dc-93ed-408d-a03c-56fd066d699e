class Appraisal < ApplicationRecord
  include HasUuid

  # Search term validation constants
  MIN_SEARCH_TERM_LENGTH = 3
  MAX_SEARCH_TERM_LENGTH = 50

  default_scope { where.not(status: :deleted) }

  belongs_to :dealership
  belongs_to :customer

  has_one :customer_vehicle, dependent: :destroy
  belongs_to :sales_person, class_name: "User"
  belongs_to :created_by, class_name: "User"
  belongs_to :updated_by, class_name: "User"

  has_many :favourite_appraisals, dependent: :destroy
  has_many :favourited_by_users, through: :favourite_appraisals, source: :user
  has_one_attached :customer_signature

  has_many :appraisal_offers, dependent: :destroy

  enum :status, {
    incomplete: 0,
    complete: 1,
    awarded: 2,
    archived: 3,
    deleted: 4
  }, default: :incomplete

  enum :previous_status, {
    incomplete: 0,
    complete: 1,
    awarded: 2,
    archived: 3,
    deleted: 4
  }, prefix: true, allow_nil: true

  enum :appraisal_status, {
    retail: 0,
    wholesale: 1,
    lost: 2
  }, default: :retail

  validates :completed_percentage, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 }, allow_nil: true
  validates :awarded_value, :price, :given_price, numericality: true, allow_nil: true
  validates :awarded_notes, length: { maximum: 1000 }, allow_blank: true
  validates :customer_signature, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }


  scope :filter_by_status, ->(status) { where(status: status) }
  scope :by_salesperson, ->(uuid) { joins(:sales_person).where(users: { uuid: uuid }) }
  scope :by_customer, ->(uuid) { joins(:customer).where(customers: { uuid: uuid }) }
  scope :by_brand, ->(uuid) { joins(customer_vehicle: :brand).where(brands: { uuid: uuid }) }
  scope :by_registration_number, ->(rego) { joins(:customer_vehicle).where(customer_vehicles: { rego: rego }) }
  scope :created_between_dates, ->(start_date = nil, end_date = nil) {
    query = all
    begin
      query = query.where(created_at: Date.parse(start_date).beginning_of_day..) if start_date.present?
      query = query.where(created_at: ..Date.parse(end_date).end_of_day) if end_date.present?
    rescue Date::Error, ArgumentError
      raise Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format"
    end
    query
  }

  scope :search_by_term, ->(term) {
    return none if term.blank?

    cleaned_term = term.strip
    return none if cleaned_term.length < MIN_SEARCH_TERM_LENGTH || cleaned_term.length > MAX_SEARCH_TERM_LENGTH

    search_term = "%#{cleaned_term.downcase}%"
    joins(:customer)
      .left_joins(:customer_vehicle)
      .where(
        "LOWER(customers.first_name) LIKE :term OR
         LOWER(customers.last_name) LIKE :term OR
         LOWER(CONCAT(customers.first_name, ' ', customers.last_name)) LIKE :term OR
         LOWER(CONCAT(customers.last_name, ' ', customers.first_name)) LIKE :term OR
         LOWER(customers.email) LIKE :term OR
         LOWER(customers.phone_number) LIKE :term OR
         LOWER(customer_vehicles.make) LIKE :term OR
         LOWER(customer_vehicles.model) LIKE :term OR
         LOWER(customer_vehicles.rego) LIKE :term OR
         CONVERT(customer_vehicles.build_year, CHAR) LIKE :term",
        term: search_term
      )
  }

  # Dashboard scopes for efficient counting
  scope :favourited_by_user, ->(user) { joins(:favourite_appraisals).where(favourite_appraisals: { user: user }) }

  def favourited_by?(user)
    return false unless user

    favourite_appraisals.exists?(user: user)
  end

  def customer_signature_url
    return nil unless customer_signature.attached?
    customer_signature.url
  end

  def attach_customer_signature(signature_file)
    raise Errors::InvalidInput, "Signature file is required" if signature_file.blank?

    customer_signature.purge if customer_signature.attached?
    customer_signature.attach(signature_file)
  end
end
