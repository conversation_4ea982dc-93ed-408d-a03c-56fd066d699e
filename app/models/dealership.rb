class Dealership < ApplicationRecord
  include HasUuid
  include HasPhoneNumber

  TIME_ZONE_NAMES = ActiveSupport::TimeZone.all.map(&:name)

  belongs_to :dealership_group, optional: true
  belongs_to :brand, optional: true

  has_phone_number(field_name: :phone, required: true)
  has_one_attached :logo
  has_one :dealership_features_setting, dependent: :destroy
  has_one :dealership_email_setting, dependent: :destroy
  has_one :dealership_terms_setting, dependent: :destroy
  has_many :dealership_alerts, dependent: :destroy
  has_many :trade_plates, dependent: :destroy

  has_many :customers, dependent: :destroy
  has_many :vehicles, class_name: "DealershipVehicle", dependent: :destroy
  has_many :drives, dependent: :destroy
  has_many :appraisals, dependent: :destroy
  has_many :appraisal_valuers, dependent: :destroy

  has_many :user_dealerships, dependent: :destroy
  has_many :users, through: :user_dealerships

  enum :status, {
    active: 0,
    suspended: 1,
    deleted: 2
  }, default: :active

  enum :country, {
    au: "Australia",
    us: "USA"
  }, default: :au

  enum :setting_date_format, {
    dd_mm_yyyy: 0,
    mm_dd_yyyy: 1,
    yyyy_mm_dd: 2
  }, default: :dd_mm_yyyy

  enum :setting_distance_unit, {
    kilometers: 0,
    miles: 1
  }, default: :kilometers

  validates :name, :status, :address_line1, :state, :postcode, :country, presence: true
  validates :email, presence: true, email_format: true
  validates :website, url: true, allow_blank: true
  validates :logo, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }
  validates :setting_time_zone, presence: true, inclusion: { in: TIME_ZONE_NAMES }

  scope :active, -> { where(status: :active) }

  def managers
    users.joins(:user_dealerships)
         .where(user_dealerships: { dealership: self, role: :dealership_admin })
  end

  def sales_people
    users.joins(:user_dealerships)
         .where(user_dealerships: { dealership: self, role: :sales_person })
  end
end
