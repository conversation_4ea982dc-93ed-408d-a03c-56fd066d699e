class AppraisalValuer < ApplicationRecord
  belongs_to :dealership
  has_many :appraisal_offers, dependent: :destroy

  enum :status, {
    active: 0,
    inactive: 1,
    deleted: 2
  }, default: :active

  validates :business_name, presence: true, length: { maximum: 255 }
  validates :email, presence: true, email_format: true, length: { maximum: 255 }
  validates :first_name, presence: true, length: { maximum: 100 }
  validates :last_name, presence: true, length: { maximum: 100 }
  validates :mobile_number, presence: true, length: { maximum: 20 }
  validates :status, presence: true

  validates :email, uniqueness: { scope: :dealership_id, case_sensitive: false }

  scope :active, -> { where(status: :active) }
  scope :inactive, -> { where(status: :inactive) }
  scope :not_deleted, -> { where.not(status: :deleted) }

  def full_name
    "#{first_name} #{last_name}"
  end
end
