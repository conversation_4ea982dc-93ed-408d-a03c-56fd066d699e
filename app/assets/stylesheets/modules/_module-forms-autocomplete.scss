// User permissions autocomplete for dealerships, groups and brands
.users .ui-autocomplete .ui-state-active {
  background-color: var(--imColSecondary);
  border: 0;
}

.autocomplete-container {
  position: relative;
  width: 100%;
}

.autocomplete-input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
}

.autocomplete-input:focus {
  outline: none;
  border-color: #3498db;
}

.autocomplete-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  border-radius: 0 0 5px 5px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.autocomplete-item {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.autocomplete-item:hover,
.autocomplete-item.selected {
  background-color: #f8f9fa;
}

.autocomplete-item:last-child {
  border-bottom: none;
}
