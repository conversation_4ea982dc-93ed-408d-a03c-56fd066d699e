<%= form_for @user, builder: CmsFormBuilder do |f| %>
  <%= render "shared/errors", object: @user %>
  <fieldset>
    <div class="col-wrapper-2">
      <!-- Basic Information -->
      <div class="imf-input-wrapper">
        <%= f.label :first_name, nil, mandatory: true %>
        <%= f.text_field :first_name, autofocus: true %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :last_name, nil, mandatory: true %>
        <%= f.text_field :last_name %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :email, nil, mandatory: true %>
        <%= f.email_field :email %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :phone, nil, mandatory: true %>
        <%= f.text_field :phone %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :job_title %>
        <%= f.text_field :job_title %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :external_id %>
        <%= f.text_field :external_id %>
      </div>

      <!-- Account Settings -->
      <div class="imf-input-wrapper">
        <%= f.label :user_type %>
        <%= f.select :user_type, options_for_select(User.user_types.collect { |user_type| [user_type[0].humanize.titleize, user_type[0]] }, selected: @user.user_type) %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :status %>
        <%= f.select :status, options_for_select(User.statuses.collect { |status| [status[0].humanize.titleize, status[0]] }, selected: @user.status) %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :preferred_language %>
        <%= f.select :preferred_language, options_for_select(User.preferred_languages.collect { |lang| [lang[0].humanize.titleize, lang[0]] }, selected: @user.preferred_language) %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :time_zone %>
        <%= f.select :time_zone, ActiveSupport::TimeZone.all.map(&:name), prompt: true %>
      </div>

      <!-- Password Settings -->
      <div class="imf-input-wrapper">
        <%= f.label :password %>
        <%= f.password_field :password %>
        <small>Leave blank to auto-generate a temporary password</small>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :password_confirmation %>
        <%= f.password_field :password_confirmation %>
      </div>

      <!-- Account Flags -->
      <div class="imf-input-wrapper">
        <%= f.label :password_change_required do %>
          <%= f.check_box :password_change_required %>
          Password change required
        <% end %>
      </div>
      <div class="imf-input-wrapper">
        <%= f.label :onboarding_completed do %>
          <%= f.check_box :onboarding_completed %>
          Onboarding completed
        <% end %>
      </div>

      <!-- Photo Upload -->
      <div class="imf-input-wrapper">
        <%= f.label :photo %>
        <%= f.file_field :photo %>
      </div>
    </div>

    <% if @user.user_type == 'dealership_user' %>
      <!-- Driver License Information -->
      <h3>Driver License Information</h3>
      <div class="col-wrapper-2">
        <%= f.fields_for :driver_license, (@user.driver_license || @user.build_driver_license) do |dl| %>
          <div class="imf-input-wrapper">
            <%= dl.label :licence_number %>
            <%= dl.text_field :licence_number %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :full_name %>
            <%= dl.text_field :full_name %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :date_of_birth %>
            <%= dl.date_field :date_of_birth %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :expiry_date %>
            <%= dl.date_field :expiry_date %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :issue_date %>
            <%= dl.date_field :issue_date %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :category %>
            <%= dl.text_field :category %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :issuing_country %>
            <%= dl.text_field :issuing_country %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :issuing_state %>
            <%= dl.text_field :issuing_state %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :verification_status %>
            <%= dl.select :verification_status, options_for_select(DriverLicense.verification_statuses.collect { |status| [status[0].humanize.titleize, status[0]] }, selected: dl.object.verification_status) %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :verification_rejection_reason %>
            <%= dl.text_area :verification_rejection_reason %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :front_image, "Front Image" %>
            <%= dl.file_field :front_image %>
            <% if dl.object.front_image.attached? %>
              <div class="current-image">
                <p>Current front image:</p>
                <%= image_tag dl.object.front_image, alt: "Driver License Front", style: "max-width: 200px; max-height: 150px;" %>
              </div>
            <% end %>
          </div>
          <div class="imf-input-wrapper">
            <%= dl.label :back_image, "Back Image" %>
            <%= dl.file_field :back_image %>
            <% if dl.object.back_image.attached? %>
              <div class="current-image">
                <p>Current back image:</p>
                <%= image_tag dl.object.back_image, alt: "Driver License Back", style: "max-width: 200px; max-height: 150px;" %>
              </div>
            <% end %>
          </div>
        <% end %>
      </div>
      <% end %>

    <%= render 'shared/cancel_and_save_buttons', cancellation_path: cancellation_path, f: f, button_name: button_name %>
  </fieldset>
<% end %>
