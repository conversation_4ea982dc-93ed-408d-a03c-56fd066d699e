<% content_for(:body_class, 'users') %>
<% content_for(:title, @user.name) %>
<% content_for(:section_heading, @user.name) %>
<main class="main">
  <div class="section-forms">
    <%= render 'shared/bread_crumb', crumbs: [
      { step: "Users", link: users_path },
      { step: "#{@user.name}" }
    ]
    %>
    <div class="section-users box-trbl">
      <ul class="list-recurring list-user-edit">
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">First Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.first_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Name</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.last_name %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Email Address</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= mail_to @user.email %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Phone</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.formatted_phone || @user.phone %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Job Title</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.job_title.presence || "—" %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">External ID</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.external_id.presence || "—" %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Time zone</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><% if @user.time_zone %><%= "(GMT#{TimeZone.offset(@user.time_zone)}) #{@user.time_zone}" %><% else %> &mdash; <% end %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">User Access</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.user_type.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Status</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.status.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Preferred Language</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.preferred_language.humanize %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Password Change Required</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.password_change_required? ? "Yes" : "No" %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Onboarding Completed</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= @user.onboarding_completed? ? "Yes" : "No" %></span></div>
        </li>
        <% if @user.photo.attached? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Photo</span></div>
          <div class="lr-col lr-col-lg">
            <%= image_tag @user.photo, alt: "#{@user.name} photo", style: "max-width: 150px; max-height: 150px;" %>
          </div>
        </li>
        <% end %>
        <% if @user.driver_license.present? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Driver License</span></div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">
              <%= @user.driver_license.licence_number %><br>
              <small>Expires: <%= @user.driver_license.expiry_date %></small><br>
              <small>Status: <%= @user.driver_license.verification_status.humanize %></small>
            </span>
          </div>
        </li>
        <% if @user.driver_license.front_image.attached? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">License Front Image</span></div>
          <div class="lr-col lr-col-lg">
            <%= image_tag @user.driver_license.front_image, alt: "Driver License Front", style: "max-width: 300px; max-height: 200px;" %>
          </div>
        </li>
        <% end %>
        <% if @user.driver_license.back_image.attached? %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">License Back Image</span></div>
          <div class="lr-col lr-col-lg">
            <%= image_tag @user.driver_license.back_image, alt: "Driver License Back", style: "max-width: 300px; max-height: 200px;" %>
          </div>
        </li>
        <% end %>
        <% end %>
        <li class="lr-item">
          <div class="lr-col lr-col-md"><span class="lr-text lr-text-first">Last Active</span></div>
          <div class="lr-col lr-col-lg"><span class="lr-text"><%= user_last_active_on(@user.last_active_on) %></span></div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(edit_user_path(@user), class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-pencil-edit-line', 'pencil-edit-line') %><span class="lr-text lr-action-text">Edit User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Edit user details</span>
          </div>
        </li>
        <li class="lr-item">
          <div class="lr-col lr-col-md">
            <%= link_to(user_path(@user), data: { turbo_method: :delete, turbo_confirm: "Are you sure you want to permanently delete #{@user.name}?" }, class: 'lr-action-wrapper') do %>
              <%= use_svg('lr-action-icon icon-trash', 'trash') %><span class="lr-text lr-action-text">Delete User</span>
            <% end %>
          </div>
          <div class="lr-col lr-col-lg">
            <span class="lr-text">Delete this user</span>
          </div>
        </li>
      </ul>


      <% if @user.user_type == 'dealership_user' %>
        <!-- Device Registrations Section -->
        <div class="box-t device-registrations-table">
          <h2>Device Registrations</h2>
          <% if @user.device_registrations.any? %>
            <h3><%= pluralize(@user.device_registrations.size, 'Device') %></h3>
            <div class="sticky-wrapper">
              <ul class="lr-header-row">
                <li class="lr-item lr-item-heading">
                  <div class="lr-col lr-col-lg"><span class="lr-text-heading">Device</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">OS & Version</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">App Version</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">Last Login</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">Last Activity</span></div>
                  <div class="lr-col lr-col-sm"><span class="lr-text-heading">Status</span></div>
                  <div class="lr-col lr-col-sm"><span class="lr-text-heading">Delete</span></div>
                </li>
              </ul>
            </div>
            <ul class="list-recurring">
              <% @user.device_registrations.order(created_at: :desc).each do |device| %>
              <li class="lr-item">
                <div class="lr-col lr-col-lg">
                  <div class="lr-text">
                    <%= device.device_name.presence || "Unknown Device" %>
                    <br><small style="color: #666;"><%= device.device_id %></small>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <%= device.device_os.humanize %>
                    <% if device.device_os_version.present? %>
                      <br><small style="color: #666;">v<%= device.device_os_version %></small>
                    <% end %>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <%= device.app_version %>
                    <% if device.app_build_number.present? %>
                      <br><small style="color: #666;">Build <%= device.app_build_number %></small>
                    <% end %>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <% if device.last_login_timestamp %>
                      <%= time_ago_in_words(device.last_login_timestamp) %> ago
                      <br><small style="color: #666;"><%= device.last_login_timestamp.strftime("%b %d, %Y") %></small>
                    <% else %>
                      <span style="color: #999;">&mdash;</span>
                    <% end %>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <% if device.last_activity_at %>
                      <%= time_ago_in_words(device.last_activity_at) %> ago
                      <br><small style="color: #666;"><%= device.last_activity_at.strftime("%b %d, %Y") %></small>
                    <% else %>
                      <span style="color: #999;">&mdash;</span>
                    <% end %>
                  </div>
                </div>
                <div class="lr-col lr-col-sm">
                  <div class="lr-text lr-text-center">
                    <% if device.active? && device.refresh_token_expires_at > Time.current %>
                      <%= use_svg('lr-icon lr-action-icon icon-tick', 'tick') %>
                    <% elsif device.active? %>
                      <%= use_svg('lr-icon lr-static-icon icon-clock', 'clock') %>
                    <% else %>
                      <%= use_svg('lr-icon lr-static-icon icon-cross', 'cross') %>
                    <% end %>
                  </div>
                </div>
                <div class="lr-col lr-col-sm">
                  <%= link_to user_device_registration_path(@user, device),
                      data: {
                        turbo_method: :delete,
                        turbo_confirm: "Are you sure you want to delete this device registration? The user will need to log in again on this device."
                      },
                      class: 'lr-link' do %>
                    <%= use_svg('lg-icon icon-trash icon-alert', 'trash') %>
                  <% end %>
                </div>
              </li>
              <% end %>
            </ul>
          <% else %>
            <div>
              <p>No device registrations found for this user.</p>
            </div>
          <% end %>
        </div>

        <!-- User Dealerships Section -->
        <div class="box-t user-dealerships-table">
          <h2>Dealership Associations</h2>
          <% if @user.user_dealerships.any? %>
            <h3><%= pluralize(@user.user_dealerships.size, 'Association') %></h3>
            <div class="sticky-wrapper">
              <ul class="lr-header-row">
                <li class="lr-item lr-item-heading">
                  <div class="lr-col lr-col-lg"><span class="lr-text-heading">Dealership</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">Role</span></div>
                  <div class="lr-col lr-col-md"><span class="lr-text-heading">Created</span></div>
                  <div class="lr-col lr-col-sm"><span class="lr-text-heading">Delete</span></div>
                </li>
              </ul>
            </div>
            <ul class="list-recurring">
              <% @user.user_dealerships.includes(:dealership).order('dealerships.name').each do |user_dealership| %>
              <li class="lr-item">
                <div class="lr-col lr-col-lg">
                  <div class="lr-text">
                    <%= user_dealership.dealership.name %>
                    <br><small style="color: #666;"><%= user_dealership.dealership.address_line1 %>, <%= user_dealership.dealership.state %></small>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <%= user_dealership.role.humanize %>
                  </div>
                </div>
                <div class="lr-col lr-col-md">
                  <div class="lr-text">
                    <%= time_ago_in_words(user_dealership.created_at) %> ago
                    <br><small style="color: #666;"><%= user_dealership.created_at.strftime("%b %d, %Y") %></small>
                  </div>
                </div>
                <div class="lr-col lr-col-sm">
                  <%= link_to user_user_dealership_path(@user, user_dealership),
                      data: {
                        turbo_method: :delete,
                        turbo_confirm: "Are you sure you want to remove this dealership association?"
                      },
                      class: 'lr-link' do %>
                    <%= use_svg('lg-icon icon-trash icon-alert', 'trash') %>
                  <% end %>
                </div>
              </li>
              <% end %>
            </ul>
          <% else %>
            <div>
              <p>No dealership associations found for this user.</p>
            </div>
          <% end %>

          <!-- Add New Association Form -->
          <div class="box-t">
            <h3>Add New Dealership Association</h3>
            <%= form_with model: [@user, UserDealership.new], local: true, class: "add-dealership-form" do |f| %>
              <fieldset>
                <div class="col-wrapper-2">
                  <div class="imf-input-wrapper">
                    <%= f.label :dealership_id, "Dealership" %>
                    <div class="autocomplete-container">
                      <%= f.text_field :search_term, class: "autocomplete-input",
                        placeholder: "Type to search...",
                        data: { autocomplete_url: dealerships_autocomplete_path, target_field: "user_dealership_dealership_id" } %>
                      <%= f.hidden_field :dealership_id %>
                      <div class="autocomplete-results"></div>
                    </div>
                  </div>
                  <div class="imf-input-wrapper">
                    <%= f.label :role %>
                    <%= f.select :role, options_for_select(UserDealership.roles.collect { |role| [role[0].humanize.titleize, role[0]] }), { prompt: "Select a role" } %>
                  </div>
                </div>
                <div class="imf-btn-wrapper">
                  <%= f.submit "Add Association", class: 'imf-btn' %>
                </div>
              </fieldset>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</main>
