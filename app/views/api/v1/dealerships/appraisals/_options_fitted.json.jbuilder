json.options_fitted do
  json.extract! options_fitted,
                :uuid,
                :has_sunroof,
                :has_tinted_windows,
                :has_towbar,
                :has_keyless_entry,
                :has_bluetooth,
                :has_ventilated_seats,
                :has_tray_fitted,
                :has_canopy_fitted,
                :has_aftermarket_wheels,
                :has_bull_bar,
                :has_extended_warranty,
                :extended_warranty_expiry,
                :ppsr,
                :additional_options,
                :sunroof_type,
                :number_of_keys,
                :heated_seats,
                :cargo_blind,
                :tonneau_cover,
                :tonneau_type,
                :on_written_off_register,
                :last_ppsr_date,
                :notes

  # Options images
  if options_fitted.options_images.attached?
    json.options_image_urls options_fitted.options_images.map { |image| image.url }
  else
    json.options_image_urls []
  end
end
