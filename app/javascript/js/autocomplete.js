CMS = CMS || {};

const autocomplete = () => {
  const loads = ["initialize_autocomplete"];

  const initializeAutocomplete = () => {
    const inputs = document.querySelectorAll('[data-autocomplete-url]');

    if (!inputs.length) return false;

    inputs.forEach(input => {
      const container = input.closest('.autocomplete-container');
      const resultsContainer = container.querySelector('.autocomplete-results');
      const url = input.dataset.autocompleteUrl;
      const targetField = document.getElementById(input.dataset.targetField);
      let currentSelection = -1;
      let timeoutId;

      input.addEventListener('input', function () {
        const query = this.value.trim();

        clearTimeout(timeoutId);

        if (query.length < 3) {
          hideResults();
          return;
        }

        // Debounce the search
        timeoutId = setTimeout(() => {
          fetch(`${url}?q=${encodeURIComponent(query)}`, {
            headers: {
              'Accept': 'application/json',
              'X-Requested-With': 'XMLHttpRequest',
              'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').content
            }
          })
            .then(response => response.json())
            .then(data => {
              displayResults(data);
            })
            .catch(error => {
              console.error('Search error:', error);
              hideResults();
            });
        }, 300);
      });

      input.addEventListener('keydown', function (e) {
        const items = resultsContainer.querySelectorAll('.autocomplete-item');

        switch (e.key) {
          case 'ArrowDown':
            e.preventDefault();
            currentSelection = Math.min(currentSelection + 1, items.length - 1);
            updateSelection(items);
            break;
          case 'ArrowUp':
            e.preventDefault();
            currentSelection = Math.max(currentSelection - 1, -1);
            updateSelection(items);
            break;
          case 'Enter':
            e.preventDefault();
            if (currentSelection >= 0 && items[currentSelection]) {
              selectItem(items[currentSelection]);
            }
            break;
          case 'Escape':
            hideResults();
            break;
        }
      });

      input.addEventListener('blur', function () {
        // Delay hiding to allow click events
        setTimeout(() => hideResults(), 150);
      });

      function displayResults(items) {
        if (items.length === 0) {
          hideResults();
          return;
        }

        resultsContainer.innerHTML = '';
        currentSelection = -1;

        items.forEach((item, index) => {
          const div = document.createElement('div');
          div.className = 'autocomplete-item';
          div.textContent = item.name;
          div.dataset.id = item.id;
          div.dataset.name = item.name;

          div.addEventListener('click', () => selectItem(div));

          resultsContainer.appendChild(div);
        });

        resultsContainer.style.display = 'block';
      }

      function selectItem(item) {
        const id = item.dataset.id;
        const name = item.dataset.name;

        input.value = name;
        if (targetField) {
          targetField.value = id;
        }

        hideResults();

        // Trigger change event for any listeners
        input.dispatchEvent(new Event('change', { bubbles: true }));
        if (targetField) {
          targetField.dispatchEvent(new Event('change', { bubbles: true }));
        }
      }

      function updateSelection(items) {
        items.forEach((item, index) => {
          item.classList.toggle('selected', index === currentSelection);
        });
      }

      function hideResults() {
        resultsContainer.style.display = 'none';
        currentSelection = -1;
      }

      // Hide results when clicking outside
      document.addEventListener('click', function (e) {
        if (!container.contains(e.target)) {
          hideResults();
        }
      });
    });

  };

  return {
    loads: loads,
    initialize_autocomplete: initializeAutocomplete
  };
};

CMS.autocomplete = autocomplete();
