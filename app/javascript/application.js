// Configure your import map in config/importmap.rb. Read more: https://github.com/rails/importmap-rails
import "@hotwired/turbo-rails"
import "./js/theme_switcher";
import "./js/cms";
// import "./js/analytics";
import "./image-cropper";
import "./js/asset_uploader_for_asset_model";
import "./js/image_uploader_for_asset_model";
import "./js/asset_uploader";
import "./js/component";
import "./js/component_instance_configurator";
import "./js/ui_pattern_radio_button";
import "./js/banner_picker";
import "./js/configuration_form";
import "./js/dealerships";
import "./js/drag_drop_functionality";
import "./js/loading_component";
import "./js/partials_injector";
import "./js/page_configurator";
import "./js/page_details_form";
import "./js/form_confirm_submission";
import "./js/form_disable_save_before_edit";
import "./js/vehicle_feature_form";
import "./js/dealer_locator_cta_form";
import "./js/radio_buttons";
import "./js/search_list";
import "./js/text_field_counter";
import "./js/vehicle_models";
import "./js/vehicle_trims";
import "./js/visibility_toggler";
import "./js/specials";
import "./js/populate_secondary_select";
import "./js/construct_button_url_using_select";
import "./js/choices";
import "./js/toggle_rule_group_rules";
import "./js/equals_and_not_equals_rule_forms";
import "./js/search_bar";
import "./js/automatically_initiate_choices_select";
import "./js/automatically_initiate_choices_text";
import "./js/testimonials";
import "./js/media_type_radio_button";
import "./js/cta_configuration_page_radio_buttons";
import "./js/page_section_type_radio_button";
import "./js/font_type_radio_button";
import "./js/check_box_div_show_hide";
import "./js/colour_picker";
import "./js/config_form";
import "./js/otp_resend_counter";
import "./js/asset_categories";
import "./js/web_notification";
import "./js/dashboard_data";
import "./js/select_submit_step_for_dynamic_form";
import "./js/nav_toggle";
import "./js/autocomplete";

document.addEventListener("turbo:load", () => {
  CMS.init();
});

//= require rails-ujs
//= require jquery
//= require jquery-ui/widgets/datepicker
//= require jquery-ui/widgets/autocomplete
//= require turbolinks
//= require polyfills
//= require flot/jquery.flot
//= require_tree ./././vendor/assets/javascripts/flot
//= require jquery.animateNumber
//= require moment
//= require bootstrap-datetimepicker
//= require jquery.table2excel
//= require froala_editor.min
//= require froala_addons
//= require tinymce
// TODO: jQuery image croppper - used now, but will be removed after fixing JS image cropper bugs
//= require cropper.min.js
// TODO: JS image cropper - will be used in future, when bugs in image-cropperjs.js file are fixed
//= require cropperjs/dist/cropper.min.js
//= require sortable
//= require list
//= require jquery.fileupload
//= require tmpl
//= require_tree .
