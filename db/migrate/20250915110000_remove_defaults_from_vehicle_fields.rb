class RemoveDefaultsFromVehicleFields < ActiveRecord::Migration[8.0]
  def change
    # customer_vehicles
    change_column_null    :customer_vehicles, :is_vehicle_present, true
    change_column_default :customer_vehicles, :is_vehicle_present, from: true, to: nil

    # finance_details
    change_column_null    :finance_details, :is_financed, true
    change_column_default :finance_details, :is_financed, from: 2, to: nil
    change_column_null    :finance_details, :has_clear_title, true
    change_column_default :finance_details, :has_clear_title, from: false, to: nil

    # vehicle_histories
    change_column_null    :vehicle_histories, :has_accident_history, true
    change_column_default :vehicle_histories, :has_accident_history, from: false, to: nil
    change_column_null    :vehicle_histories, :has_dash_warning_lights, true
    change_column_default :vehicle_histories, :has_dash_warning_lights, from: false, to: nil
    change_column_null    :vehicle_histories, :vehicle_history_status, true
    change_column_default :vehicle_histories, :vehicle_history_status, from: 0, to: nil

    # vehicle_conditions
    change_column_null    :vehicle_conditions, :is_clean, true
    change_column_default :vehicle_conditions, :is_clean, from: true, to: nil
    change_column_null    :vehicle_conditions, :is_wet, true
    change_column_default :vehicle_conditions, :is_wet, from: false, to: nil
    change_column_null    :vehicle_conditions, :is_road_tested, true
    change_column_default :vehicle_conditions, :is_road_tested, from: false, to: nil
    change_column_null    :vehicle_conditions, :has_signs_of_repair, true
    change_column_default :vehicle_conditions, :has_signs_of_repair, from: false, to: nil
  end
end
