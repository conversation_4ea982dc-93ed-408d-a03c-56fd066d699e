class RemoveDefaultValuesFromCustomerVehicleEnums < ActiveRecord::Migration[8.0]
  def change
    change_column_default :customer_vehicles, :seat_type, from: 0, to: nil
    change_column_default :customer_vehicles, :fuel_type, from: 0, to: nil
    change_column_default :customer_vehicles, :driving_wheels, from: 0, to: nil
    change_column_default :customer_vehicles, :spare_wheel_type, from: 0, to: nil
    change_column_default :customer_vehicles, :transmission, from: 0, to: nil
    change_column_default :customer_vehicles, :body_type, from: 0, to: nil
  end
end
