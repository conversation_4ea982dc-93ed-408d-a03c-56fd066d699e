class ChangeOptionsFittedBooleansToAllowNull < ActiveRecord::Migration[8.0]
  def change
    # Change all boolean fields to allow null and remove default false values
    change_column_null :options_fitted, :has_sunroof, true
    change_column_default :options_fitted, :has_sunroof, from: false, to: nil

    change_column_null :options_fitted, :has_tinted_windows, true
    change_column_default :options_fitted, :has_tinted_windows, from: false, to: nil

    change_column_null :options_fitted, :has_towbar, true
    change_column_default :options_fitted, :has_towbar, from: false, to: nil

    change_column_null :options_fitted, :has_keyless_entry, true
    change_column_default :options_fitted, :has_keyless_entry, from: false, to: nil

    change_column_null :options_fitted, :has_bluetooth, true
    change_column_default :options_fitted, :has_bluetooth, from: false, to: nil

    change_column_null :options_fitted, :has_ventilated_seats, true
    change_column_default :options_fitted, :has_ventilated_seats, from: false, to: nil

    change_column_null :options_fitted, :has_tray_fitted, true
    change_column_default :options_fitted, :has_tray_fitted, from: false, to: nil

    change_column_null :options_fitted, :has_canopy_fitted, true
    change_column_default :options_fitted, :has_canopy_fitted, from: false, to: nil

    change_column_null :options_fitted, :has_aftermarket_wheels, true
    change_column_default :options_fitted, :has_aftermarket_wheels, from: false, to: nil

    change_column_null :options_fitted, :has_bull_bar, true
    change_column_default :options_fitted, :has_bull_bar, from: false, to: nil

    change_column_null :options_fitted, :has_extended_warranty, true
    change_column_default :options_fitted, :has_extended_warranty, from: false, to: nil

    change_column_null :options_fitted, :ppsr, true
    change_column_default :options_fitted, :ppsr, from: false, to: nil

    change_column_null :options_fitted, :heated_seats, true
    change_column_default :options_fitted, :heated_seats, from: false, to: nil

    change_column_null :options_fitted, :cargo_blind, true
    change_column_default :options_fitted, :cargo_blind, from: false, to: nil

    change_column_null :options_fitted, :tonneau_cover, true
    change_column_default :options_fitted, :tonneau_cover, from: false, to: nil
  end
end
