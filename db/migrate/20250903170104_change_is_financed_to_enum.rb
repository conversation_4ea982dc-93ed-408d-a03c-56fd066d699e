class ChangeIsFinancedToEnum < ActiveRecord::Migration[8.0]
  def change
    reversible do |dir|
      dir.up do
        # Update existing data: true -> 0 (financed), false -> 1 (not_financed)
        execute "UPDATE finance_details SET is_financed = CASE WHEN is_financed = true THEN 0 ELSE 1 END"

        # Change column type from boolean to integer
        change_column :finance_details, :is_financed, :integer, default: 1, null: false
      end

      dir.down do
        # Update existing data: 0 (financed) -> true, 1 (not_financed) -> false, 2 (unknown) -> false
        execute "UPDATE finance_details SET is_financed = CASE WHEN is_financed = 0 THEN true ELSE false END"

        # Change column type back to boolean
        change_column :finance_details, :is_financed, :boolean, default: false, null: false
      end
    end
  end
end
