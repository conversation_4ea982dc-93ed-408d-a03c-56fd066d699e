class CreateAppraisalOffers < ActiveRecord::Migration[8.0]
  def change
    create_table :appraisal_offers do |t|
      t.references :appraisal_valuer, foreign_key: true
      t.references :appraisal, null: false, foreign_key: true
      t.date :offer_date
      t.decimal :offer_price, precision: 15, scale: 2
      t.text :offer_notes
      t.boolean :is_verbal_offer, null: false, default: false
      t.string :valuer_business_name
      t.string :valuer_email
      t.string :valuer_first_name
      t.string :valuer_last_name
      t.string :valuer_mobile_number

      t.timestamps
    end
  end
end
