puts "Seeding appraisals..."

unless Dealership.any?
  puts "Dealerships missing. Please run dealership seeds first."
  return
end

unless Customer.any?
  puts "Customers missing. Please run customer seeds first."
  return
end

unless User.sales_people.any?
  puts "Sales People missing. Please run user seeds first."
  return
end

dealership1 = Dealership.first
dealership2 = Dealership.second

# Base appraisal data
base_appraisals = [
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 25,
    notes: "Initial appraisal - customer interested in trade-in"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 25000.00,
    price: 28000.00,
    given_price: 26500.00,
    awarded_notes: "Excellent condition vehicle, minor wear on interior",
    notes: "Comprehensive appraisal completed - customer satisfied with offer"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 18500.00,
    price: 22000.00,
    given_price: 19000.00,
    awarded_notes: "Good condition, some paint touch-ups required",
    notes: "Customer accepted offer - proceeding with trade-in"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 60,
    price: 15000.00,
    notes: "Waiting for mechanical inspection report"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 32000.00,
    price: 35000.00,
    given_price: 33000.00,
    awarded_notes: "Premium vehicle in excellent condition",
    notes: "High-value appraisal - luxury vehicle"
  },
  {
    dealership: dealership1,
    status: :archived,
    completed_percentage: 100,
    awarded_value: 12000.00,
    price: 14500.00,
    given_price: 12500.00,
    awarded_notes: "Older vehicle with high mileage",
    notes: "Customer decided not to proceed with trade-in"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 40,
    notes: "Customer requested appraisal for insurance purposes"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 21000.00,
    price: 24000.00,
    given_price: 22000.00,
    awarded_notes: "Well-maintained vehicle, service history complete",
    notes: "Standard appraisal - good condition vehicle"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 16500.00,
    price: 19000.00,
    given_price: 17500.00,
    awarded_notes: "Minor cosmetic issues, mechanically sound",
    notes: "Customer accepted offer for immediate trade-in"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 15,
    notes: "Initial inspection scheduled for next week"
  },
    # High-value luxury vehicles
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 45000.00,
    price: 48000.00,
    given_price: 46500.00,
    awarded_notes: "Luxury sedan in pristine condition, full service history",
    notes: "Premium vehicle appraisal - BMW 5 Series equivalent"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 38000.00,
    price: 42000.00,
    given_price: 39500.00,
    awarded_notes: "Mercedes-Benz equivalent, minor wear on leather seats",
    notes: "Customer accepted premium offer - luxury trade-in"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 52000.00,
    price: 55000.00,
    given_price: 53500.00,
    awarded_notes: "Audi Q7 equivalent, excellent condition, low mileage",
    notes: "High-end SUV appraisal - exceptional vehicle"
  },

  # Mid-range family vehicles
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 70,
    price: 28000.00,
    notes: "Family SUV appraisal in progress - Toyota RAV4 equivalent"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 24500.00,
    price: 27000.00,
    given_price: 25500.00,
    awarded_notes: "Honda Civic equivalent, well-maintained, one owner",
    notes: "Popular family sedan - good market value"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 31000.00,
    price: 34000.00,
    given_price: 32000.00,
    awarded_notes: "Mazda CX-5 equivalent, excellent condition, recent service",
    notes: "Customer proceeding with trade-in - popular SUV model"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 45,
    notes: "Subaru Outback equivalent - waiting for detailed inspection"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 26000.00,
    price: 29000.00,
    given_price: 27500.00,
    awarded_notes: "Nissan X-Trail equivalent, good condition, minor scratches",
    notes: "Standard family SUV appraisal"
  },

  # Economy and compact vehicles
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 15500.00,
    price: 18000.00,
    given_price: 16500.00,
    awarded_notes: "Toyota Corolla equivalent, high mileage but well-maintained",
    notes: "Economy vehicle - good value for age and condition"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 13000.00,
    price: 15500.00,
    given_price: 14000.00,
    awarded_notes: "Hyundai i30 equivalent, some wear but mechanically sound",
    notes: "Customer accepted offer - compact hatchback trade-in"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 30,
    notes: "Kia Cerato equivalent - initial inspection completed"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 17500.00,
    price: 20000.00,
    given_price: 18500.00,
    awarded_notes: "Volkswagen Golf equivalent, good condition, service history available",
    notes: "Popular compact car - strong market demand"
  },

  # Commercial and utility vehicles
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 35000.00,
    price: 38000.00,
    given_price: 36500.00,
    awarded_notes: "Ford Ranger equivalent, excellent condition, low commercial use",
    notes: "Popular utility vehicle - strong trade-in value"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 42000.00,
    price: 45000.00,
    given_price: 43500.00,
    awarded_notes: "Toyota HiLux equivalent, well-maintained, service history complete",
    notes: "Customer accepted offer - premium ute trade-in"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 55,
    price: 32000.00,
    notes: "Isuzu D-Max equivalent - mechanical inspection in progress"
  },

  # Electric and hybrid vehicles
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 28000.00,
    price: 31000.00,
    given_price: 29500.00,
    awarded_notes: "Toyota Prius equivalent, excellent fuel economy, battery in good condition",
    notes: "Hybrid vehicle appraisal - growing market segment"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 80,
    notes: "Tesla Model 3 equivalent - battery assessment pending"
  },
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 35000.00,
    price: 38000.00,
    given_price: 36500.00,
    awarded_notes: "Nissan Leaf equivalent, low mileage, charging equipment included",
    notes: "Electric vehicle appraisal - premium for low mileage"
  },

  # Sports and performance vehicles
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 48000.00,
    price: 52000.00,
    given_price: 50000.00,
    awarded_notes: "Ford Mustang equivalent, excellent condition, performance modifications",
    notes: "Sports car appraisal - enthusiast vehicle"
  },
  {
    dealership: dealership1,
    status: :awarded,
    completed_percentage: 100,
    awarded_value: 55000.00,
    price: 58000.00,
    given_price: 56500.00,
    awarded_notes: "BMW M3 equivalent, pristine condition, track-ready",
    notes: "Customer accepted premium offer - high-performance vehicle"
  },

  # Older vehicles and high mileage
  {
    dealership: dealership1,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 8500.00,
    price: 11000.00,
    given_price: 9500.00,
    awarded_notes: "2015 model, high mileage but reliable, some cosmetic issues",
    notes: "Older vehicle appraisal - fair condition for age"
  },
  {
    dealership: dealership1,
    status: :archived,
    completed_percentage: 100,
    awarded_value: 6000.00,
    price: 8500.00,
    given_price: 7000.00,
    awarded_notes: "2013 model, significant wear, mechanical issues noted",
    notes: "Customer declined offer - vehicle condition below expectations"
  },

  # Various incomplete appraisals
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 10,
    notes: "Initial customer inquiry - vehicle details being gathered"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 35,
    notes: "Physical inspection completed - awaiting market analysis"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 65,
    notes: "Detailed assessment complete - finalizing valuation"
  },
  {
    dealership: dealership1,
    status: :incomplete,
    completed_percentage: 90,
    notes: "Appraisal nearly complete - final management approval pending"
  },

  # Archived appraisals with various reasons
  {
    dealership: dealership1,
    status: :archived,
    completed_percentage: 100,
    awarded_value: 22000.00,
    price: 25000.00,
    given_price: 23500.00,
    awarded_notes: "Good condition vehicle, competitive market value",
    notes: "Customer found better offer elsewhere - archived for reference"
  },
  {
    dealership: dealership1,
    status: :archived,
    completed_percentage: 75,
    notes: "Customer changed mind about selling - archived incomplete appraisal"
  },

  # Recent appraisals for dealership2
  {
    dealership: dealership2,
    status: :incomplete,
    completed_percentage: 25,
    notes: "New customer inquiry - initial assessment"
  },
  {
    dealership: dealership2,
    status: :complete,
    completed_percentage: 100,
    awarded_value: 19500.00,
    price: 22000.00,
    given_price: 20500.00,
    awarded_notes: "Standard family vehicle, good maintenance record",
    notes: "Completed appraisal - awaiting customer decision"
  }
]

puts "Creating base appraisals..."
base_appraisals.each_with_index do |appraisal_data, i|
  dealership = appraisal_data[:dealership]
  customer = dealership.customers.order("RAND()").first
  sales_person = dealership.sales_people.order("RAND()").first
  created_by = dealership.users.order("RAND()").first

  if customer && sales_person && created_by
    appraisal = Appraisal.create!(
      dealership: dealership,
      customer: customer,
      sales_person: sales_person,
      created_by: created_by,
      updated_by: created_by,
      **appraisal_data.except(:dealership)
    )

    puts "Created appraisal #{i + 1} for #{customer.full_name} - #{appraisal.status} (#{appraisal.completed_percentage}%)"
  else
    puts "Skipping appraisal #{i + 1}: Missing required data"
  end
end


# Create customer vehicles for appraisals
puts "Creating customer vehicles for appraisals..."
dealership1.appraisals.each do |appraisal|
  vehicle = CustomerVehicle.create!(
    appraisal: appraisal,
    customer: appraisal.customer,
    dealership: appraisal.dealership,
    brand: Brand.all.sample,
    make: Faker::Vehicle.make,
    model: Faker::Vehicle.model,
    build_year: rand(2010..Date.current.year),
    exterior_color: Faker::Vehicle.color,
    interior_color: Faker::Vehicle.color,
    vin: Faker::Vehicle.vin,
    rego: Faker::Vehicle.license_plate,
    compliance_month: rand(1..12),
    compliance_year: rand(2010..Date.current.year),
    odometer_reading: Faker::Number.between(from: 10000, to: 200000),
    number_of_doors: [2, 4, 5].sample,
    number_of_seats: [2, 4, 5, 7, 8].sample,
    seat_type: CustomerVehicle.seat_types.keys.sample,
    fuel_type: CustomerVehicle.fuel_types.keys.sample,
    driving_wheels: CustomerVehicle.driving_wheels.keys.sample,
    spare_wheel_type: CustomerVehicle.spare_wheel_types.keys.sample,
    transmission: CustomerVehicle.transmissions.keys.sample,
    body_type: CustomerVehicle.body_types.keys.sample,
    wheel_size_front: rand(16..20),
    wheel_size_rear: rand(16..20),
    engine_size: "#{rand(1.0..6.0).round(1)}L",
    engine_kilowatts: rand(80..300)
  )

  # Add finance details
  Vehicle::FinanceDetails.create!(
    customer_vehicle: vehicle,
    is_financed: [:financed, :not_financed, :unknown].sample,
    has_clear_title: [true, false].sample,
    current_repayment_amount: rand(200..1500),
    terms_months: [12, 24, 36, 48, 60, 72, 84].sample,
    interest_rate: rand(3.0..15.0).round(2),
    next_due_date: rand(1..30).days.from_now,
    finance_company: ["ANZ", "CBA", "Westpac", "NAB", "Macquarie", "Pepper Money"].sample,
    payout_amount: rand(5000..50000)
  )

  # Add options fitted
  has_extended_warranty = [true, false].sample
  Vehicle::OptionsFitted.create!(
    customer_vehicle: vehicle,
    has_sunroof: [true, false].sample,
    has_tinted_windows: [true, false].sample,
    has_towbar: [true, false].sample,
    has_keyless_entry: [true, false].sample,
    has_bluetooth: [true, false].sample,
    has_ventilated_seats: [true, false].sample,
    has_tray_fitted: [true, false].sample,
    has_canopy_fitted: [true, false].sample,
    has_aftermarket_wheels: [true, false].sample,
    has_bull_bar: [true, false].sample,
    has_extended_warranty: has_extended_warranty,
    extended_warranty_expiry: has_extended_warranty ? rand(1..24).months.from_now : nil,
    ppsr: [true, false].sample,
    sunroof_type: Vehicle::OptionsFitted.sunroof_types.keys.sample,
    number_of_keys: [1, 2, 3].sample,
    heated_seats: [true, false].sample,
    cargo_blind: [true, false].sample,
    tonneau_cover: [true, false].sample,
    tonneau_type: Vehicle::OptionsFitted.tonneau_types.keys.sample,
    on_written_off_register: Vehicle::OptionsFitted.on_written_off_registers.keys.sample,
    last_ppsr_date: [true, false].sample ? rand(1..365).days.ago : nil
  )

  # Add vehicle condition
  condition = Vehicle::VehicleCondition.create!(
    customer_vehicle: vehicle,
    is_clean: [true, false].sample,
    is_wet: [true, false].sample,
    is_road_tested: [true, false].sample,
    has_signs_of_repair: [true, false].sample,
    repair_details: [true, false].sample ? "Minor paint touch-ups on rear bumper" : nil,
    additional_notes: ["Excellent condition", "Good condition", "Fair condition", "Needs attention"].sample
  )

  # Add component ratings
  Vehicle::ComponentRating.names.keys.each do |component|
    Vehicle::ComponentRating.create!(
      vehicle_condition: condition,
      name: component,
      rating: rand(1..5)
    )
  end

  # Add reconditioning costs
  rand(0..3).times do
    cost_type = Vehicle::ReconditioningCost.cost_types.keys.sample
    next if condition.reconditioning_costs.exists?(cost_type: cost_type)

    Vehicle::ReconditioningCost.create!(
      vehicle_condition: condition,
      cost_type: cost_type,
      amount: rand(50000..500000), # $500 to $5000
      currency: "AUD"
    )
  end

  # Add body part conditions
  rand(0..3).times do
    part_name = Vehicle::BodyPartCondition.part_names.keys.sample
    next if condition.body_part_conditions.exists?(part_name: part_name)

    condition.body_part_conditions.create!(
      part_name: part_name,
      condition: Vehicle::BodyPartCondition.conditions.keys.sample,
      description: Faker::Lorem.sentence
    )
  end

  # Add vehicle history
  Vehicle::VehicleHistory.create!(
    customer_vehicle: vehicle,
    number_of_owners: rand(1..4),
    has_accident_history: [true, false].sample,
    accident_details: [true, false].sample ? "Minor rear-end collision in 2020" : nil,
    last_service_date: rand(30..365).days.ago,
    last_service_odometer: vehicle.odometer_reading - rand(1000..5000),
    next_service_due: rand(30..180).days.from_now,
    has_dash_warning_lights: [true, false].sample,
    dash_warning_details: [true, false].sample ? "Check engine light intermittent" : nil,
    notes: ["Well maintained", "Regular servicing", "Some maintenance required", "Excellent history"].sample,
    vehicle_history_status: Vehicle::VehicleHistory.vehicle_history_statuses.keys.sample
  )

end
puts "Created customer vehicles with full details"

puts "Appraisals seeded successfully!"
