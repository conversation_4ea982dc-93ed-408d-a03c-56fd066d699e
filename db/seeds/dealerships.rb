# Create Dealership Groups
puts "Creating dealership groups..."

group1 = DealershipGroup.create!(
  name: "AutoNation Australia",
  description: "Australia's largest automotive retail group",
  signup_code: "AUTO2024",
  status: :active,
  contract_end_date: 1.year.from_now
)

group2 = DealershipGroup.create!(
  name: "DriveTime Motors",
  description: "Independent dealership network",
  signup_code: "DRIVE2024",
  status: :active,
  contract_end_date: 2.years.from_now
)

# Create Dealerships
puts "Creating dealerships..."

toyota = Brand.find_by!(name: "Toyota")
honda = Brand.find_by!(name: "Honda")

toyota.logo.attach(
  io: File.open(Rails.root.join("spec/fixtures/files/toyota_logo.png").to_s),
  filename: "toyota_logo.png",
  content_type: "image/png"
)

# Dealerships for AutoNation
dealership1 = Dealership.create!(
  uuid: "9d7267a7-108b-4e62-9d6d-77cd3d86d787",
  brand: toyota,
  name: "AutoNation Toyota Melbourne",
  long_name: "AutoNation Toyota Melbourne CBD",
  status: :active,
  abn: "***********",
  address_line1: "123 Swanston Street",
  suburb: "Melbourne",
  state: "Victoria",
  postcode: "3000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  website: "https://www.autonation.com.au",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Melbourne",
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

dealership1.logo.attach(
  io: File.open(Rails.root.join("spec/fixtures/files/toyota_logo.png").to_s),
  filename: "toyota_logo.png",
  content_type: "image/png"
)

# Create settings for dealership1
dealership1.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  fuel_level_in_test_drive: true,
  fuel_level_in_loan: true,
  setting_recent_customer_age: 90
)

dealership1.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Toyota Melbourne"
)

dealership1.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms.",
  test_drive_terms_text: "<p>Test Drive Agreement</p>
<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions:</p>
<p>A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.</p>
<p>The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.</p>
<p>I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.</p>
<p>I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions:</p>
<p>I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.</p>
<p>I am a licensed driver and I am not under the influence of alcohol or drugs.</p>
<p>I understand that I am responsible for the safety of the vehicle and its passengers.</p>
<p>I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the above mentioned vehicle is in my care and control.</p>
<p>I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.</p>
<p>I agree that the vehicle will not be driven more than a 50km radius from the dealership.</p>
<p>If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Insurance Excess: $2,500</p>",
  car_loan_terms_text: "<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions: -<br />A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.<br />The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.<br />I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.<br />AND I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions: -<br />I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.<br />I am a licensed driver and I am not under the influence of alcohol or drugs.<br />I understand that I am responsible for the safety of the vehicle and its passengers.<br />I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the abovementioned vehicle is in my care and control.<br />I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.<br />I agree that the vehicle will not be driven more than a 50km radius of the dealership.<br />If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Excess Reduction Option: I agree to pay $40.00 upfront to reduce the dealerships Insurance Excess from $5,000 to $750 for the period of the loan up to a maximum of 7 days.</p>
<p>Insurance Excess: $5,000</p>"
)


dealership2 = Dealership.create!(
  uuid: "24f067e3-4f46-45bc-adf2-b5a36de40d1b",
  brand: honda,
  name: "AutoNation Honda Sydney",
  long_name: "AutoNation Honda Sydney Central",
  status: :active,
  abn: "***********",
  address_line1: "456 George Street",
  suburb: "Sydney",
  state: "New South Wales",
  postcode: "2000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Sydney",
  setting_distance_unit: :kilometers,
  dealership_group: group1
)

# Create settings for dealership2
dealership2.create_dealership_features_setting!(
  advance_booking_enabled: true,
  insurance_waiver_enabled: true,
  dealer_drive_subscription: true,
  appraisals_subscription: true,
  fuel_level_in_test_drive: true,
  fuel_level_in_loan: false,
  setting_recent_customer_age: 90
)

dealership2.create_dealership_email_setting!(
  send_test_drive_review_email: true,
  send_test_drive_terms_email: true,
  send_loan_terms_email: true,
  loan_review_email_enabled: true,
  send_email_for_bookings: :both,
  level1_odometer_warning_km: 1000,
  level1_odometer_warning_email: "<EMAIL>",
  level2_odometer_warning_km: 2000,
  level2_odometer_warning_email: "<EMAIL>",
  email_from_address: "<EMAIL>",
  email_display_name: "AutoNation Honda Sydney"
)

dealership2.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms.",
  test_drive_terms_text: "<p>Test Drive Agreement</p>
<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions:</p>
<p>A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.</p>
<p>The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.</p>
<p>I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.</p>
<p>I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions:</p>
<p>I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.</p>
<p>I am a licensed driver and I am not under the influence of alcohol or drugs.</p>
<p>I understand that I am responsible for the safety of the vehicle and its passengers.</p>
<p>I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the above mentioned vehicle is in my care and control.</p>
<p>I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.</p>
<p>I agree that the vehicle will not be driven more than a 50km radius from the dealership.</p>
<p>If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Insurance Excess: $2,500</p>",
  car_loan_terms_text: "<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions: -<br />A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.<br />The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.<br />I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.<br />AND I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions: -<br />I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.<br />I am a licensed driver and I am not under the influence of alcohol or drugs.<br />I understand that I am responsible for the safety of the vehicle and its passengers.<br />I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the abovementioned vehicle is in my care and control.<br />I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.<br />I agree that the vehicle will not be driven more than a 50km radius of the dealership.<br />If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Excess Reduction Option: I agree to pay $40.00 upfront to reduce the dealerships Insurance Excess from $5,000 to $750 for the period of the loan up to a maximum of 7 days.</p>
<p>Insurance Excess: $5,000</p>"
)

# Dealerships for DriveTime
dealership3 = Dealership.create!(
  uuid: "aabf64f3-95f9-46a4-9e83-eac361c48934",
  name: "DriveTime Perth",
  long_name: "DriveTime Perth CBD",
  status: :active,
  abn: "***********",
  address_line1: "789 Hay Street",
  suburb: "Perth",
  state: "Western Australia",
  postcode: "6000",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Perth",
  setting_distance_unit: :kilometers,
  dealership_group: group2
)

# Create settings for dealership3
dealership3.create_dealership_features_setting!(
  advance_booking_enabled: false,
  insurance_waiver_enabled: false,
  dealer_drive_subscription: false,
  appraisals_subscription: false,
  fuel_level_in_test_drive: false,
  fuel_level_in_loan: false,
  setting_recent_customer_age: 60
)

dealership3.create_dealership_email_setting!(
  send_test_drive_review_email: false,
  send_test_drive_terms_email: false,
  send_loan_terms_email: false,
  loan_review_email_enabled: false,
  send_email_for_bookings: :none,
  email_from_address: "<EMAIL>",
  email_display_name: "DriveTime Perth"
)

dealership3.create_dealership_terms_setting!(
  insurance_waiver_text: "By proceeding with the test drive, you agree to our insurance terms.",
  test_drive_terms_text: "<p>Test Drive Agreement</p>
<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions:</p>
<p>A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.</p>
<p>The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.</p>
<p>I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.</p>
<p>I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions:</p>
<p>I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.</p>
<p>I am a licensed driver and I am not under the influence of alcohol or drugs.</p>
<p>I understand that I am responsible for the safety of the vehicle and its passengers.</p>
<p>I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the above mentioned vehicle is in my care and control.</p>
<p>I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.</p>
<p>I agree that the vehicle will not be driven more than a 50km radius from the dealership.</p>
<p>If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Insurance Excess: $2,500</p>",
  car_loan_terms_text: "<p>It is the policy of this Company that we are happy to allow any prospective customer to test drive any of our New/Pre-Owned Vehicles subject to the following conditions: -<br />A current driver&rsquo;s licence, and one other form of identification is required to be photocopied prior to the commencement of the test drive.<br />The provider reserves the right to gain possession of the vehicle at any time notwithstanding that the vehicle is not due back at the time.<br />I consent to the collection and use of my personal information in line with this company&rsquo;s Privacy Statement. I understand that this information may be stored and used for the purposes of following up on my sales enquiry.<br />AND I hereby acknowledge that the information supplied is correct and in signing this Agreement, I will abide by the following conditions: -<br />I agree to drive the vehicle in a responsible manner, adhering to posed speed sign and VicRoads regulations at all times.<br />I am a licensed driver and I am not under the influence of alcohol or drugs.<br />I understand that I am responsible for the safety of the vehicle and its passengers.<br />I accept responsibility for any traffic infringements (e.g. speeding fines, parking fines and red light camera fines) whilst the abovementioned vehicle is in my care and control.<br />I accept that any tolls incurred by motorways, city link roads or bridges will be at my expense.<br />I agree that the vehicle will not be driven more than a 50km radius of the dealership.<br />If involved in an accident, I will record and provide the other party&rsquo;s particulars, including Driver&rsquo;s Licence number and Insurance Company details and pay any policy and age excess where applicable.</p>
<p>Excess Reduction Option: I agree to pay $40.00 upfront to reduce the dealerships Insurance Excess from $5,000 to $750 for the period of the loan up to a maximum of 7 days.</p>
<p>Insurance Excess: $5,000</p>"
)

# Create Dealership Alerts
puts "Creating dealership alerts..."

# Alerts for AutoNation Toyota Melbourne
DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

DealershipAlert.create!(
  dealership: dealership1,
  alert_type: :error,
  threshold: 10,
  emails: "<EMAIL>,<EMAIL>,<EMAIL>"
)

# Alerts for AutoNation Honda Sydney
DealershipAlert.create!(
  dealership: dealership2,
  alert_type: :warning,
  threshold: 5,
  emails: "<EMAIL>,<EMAIL>"
)

# Alerts for DriveTime Perth
DealershipAlert.create!(
  dealership: dealership3,
  alert_type: :info,
  threshold: 3,
  emails: "<EMAIL>"
)

Dealership.create!(
  name: "Test Dealership",
  long_name: "Test Dealership Long Name",
  status: :active,
  abn: "***********",
  address_line1: "123 Test Street",
  suburb: "Test Suburb",
  state: "Test State",
  postcode: "12345",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  website: "https://www.testdealership.com.au",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Melbourne",
  setting_distance_unit: :kilometers
)

Dealership.create!(
  name: "Test Dealership 2",
  long_name: "Test Dealership 2 Long Name",
  status: :active,
  abn: "***********",
  address_line1: "123 Test Street",
  suburb: "Test Suburb",
  state: "Test State",
  postcode: "12345",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  website: "https://www.testdealership2.com.au",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Melbourne",
  setting_distance_unit: :kilometers
)

Dealership.create!(
  name: "Test Dealership 3",
  long_name: "Test Dealership 3 Long Name",
  status: :active,
  abn: "***********",
  address_line1: "123 Test Street",
  suburb: "Test Suburb",
  state: "Test State",
  postcode: "12345",
  country: :au,
  phone: "+***********",
  email: "<EMAIL>",
  website: "https://www.testdealership3.com.au",
  setting_date_format: :dd_mm_yyyy,
  setting_time_zone: "Melbourne",
  setting_distance_unit: :kilometers
)



puts "Dealership Seed created successfully!"
