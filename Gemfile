source "https://rubygems.org"

# Bundle edge Rails instead: gem "rails", github: "rails/rails", branch: "main"
gem "rails", "~> 8.0.2"

gem "active_storage_validations"
gem "aws-sdk-s3", "~> 1"
# Reduces boot times through caching; required in config/boot.rb
gem "bootsnap", require: false
# better and maintained serializer
gem "blueprinter"
gem "cssbundling-rails"
# Auth
gem "devise"
gem "devise-jwt"
# Use JavaScript with ESM import maps [https://github.com/rails/importmap-rails]
gem "importmap-rails"
# Build JSON APIs with ease [https://github.com/rails/jbuilder]
gem "jbuilder"
gem "jsbundling-rails"
# Auth
gem "jwt"
# Deploy this application anywhere as a Docker container [https://kamal-deploy.org]
gem "kamal", require: false
# Use mysql as the database for Active Record
gem "mysql2", "~> 0.5"
gem "pagy"
gem "phonelib"

# The modern asset pipeline for Rails [https://github.com/rails/propshaft]
gem "propshaft"
# Use the Puma web server [https://github.com/puma/puma]
gem "puma", ">= 5.0"
# Admin panel
gem "rails_admin"
gem "rails_best_practices"
gem "request_store"
gem "rswag"
# Redis Cache for app and sidekiq
gem "redis"
# Background jobs
gem "sidekiq"
# Use the database-backed adapters for Rails.cache, Active Job, and Action Cable
gem "solid_cable"
gem "solid_cache"
gem "solid_queue"
# Add HTTP asset caching/compression and X-Sendfile acceleration to Puma [https://github.com/basecamp/thruster/]
gem "thruster", require: false
# Twilio for SMS
gem "twilio-ruby", "~> 7.6.3", require: true
# Windows does not include zoneinfo files, so bundle the tzinfo-data gem
gem "tzinfo-data", platforms: %i[ windows jruby ]

# # Hotwire's SPA-like page accelerator [https://turbo.hotwired.dev]
gem "turbo-rails"
# # Hotwire's modest JavaScript framework [https://stimulus.hotwired.dev]
gem "stimulus-rails"
# Use Active Storage variants [https://guides.rubyonrails.org/active_storage_overview.html#transforming-images]
gem "image_processing"

# Embed SVG documents in your Rails views
# https://github.com/jamesmartin/inline_svg
gem "inline_svg"

gem "font-awesome-sass"

# TinyMCE Text editor
gem "tinymce-rails"

# Use jquery as the JavaScript library
gem "jquery-rails"
# Use jquery-ui for datepicker. Read more: https://github.com/joliss/jquery-ui-rails
gem "jquery-ui-rails"

# Two-factor authentication
gem "rotp", "~> 6.3"  # For TOTP generation and verification
gem "rqrcode", "~> 2.2"  # For QR code generation
gem "chunky_png"  # Required by rqrcode for PNG generation

# required in seeds data
gem "faker"

group :development, :test do
  # Static analysis for security vulnerabilities [https://brakemanscanner.org/]
  gem "brakeman", require: false
  # See https://guides.rubyonrails.org/debugging_rails_applications.html#debugging-with-the-debug-gem
  gem "debug", platforms: %i[ mri windows ], require: "debug/prelude"
  gem "dotenv-rails"
  gem "factory_bot_rails"
  gem "rspec-rails"
  # Omakase Ruby styling [https://github.com/rails/rubocop-rails-omakase/]
  gem "rubocop-rails-omakase", require: false
  gem "shoulda-matchers", "~> 6.0"
end

group :development do
  # Use console on exceptions pages [https://github.com/rails/web-console]
  gem "web-console"
  gem "listen"
end

group :test do
  # Use system testing [https://guides.rubyonrails.org/testing.html#system-testing]
  gem "capybara"
  gem "selenium-webdriver"
  gem "simplecov", require: false
  gem "byebug"
end
