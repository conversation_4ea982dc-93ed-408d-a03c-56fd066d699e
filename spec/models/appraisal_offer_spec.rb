require 'rails_helper'

RSpec.describe AppraisalOffer, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:appraisal_valuer).optional }
    it { is_expected.to belong_to(:appraisal) }
  end

  describe 'validations' do
    subject { build(:appraisal_offer) }

    it { is_expected.to validate_numericality_of(:offer_price).is_greater_than(0) }
    it { is_expected.to validate_length_of(:offer_notes).is_at_most(1000).allow_blank }


    describe 'offer_price validation' do
      it 'rejects zero price' do
        offer = build(:appraisal_offer, offer_price: 0)
        expect(offer).not_to be_valid
        expect(offer.errors[:offer_price]).to include('must be greater than 0')
      end

      it 'rejects negative price' do
        offer = build(:appraisal_offer, offer_price: -100)
        expect(offer).not_to be_valid
        expect(offer.errors[:offer_price]).to include('must be greater than 0')
      end

      it 'accepts positive price' do
        valuer = create(:appraisal_valuer)
        offer = build(:appraisal_offer, appraisal_valuer: valuer, offer_price: 15000.50)
        expect(offer).to be_valid
      end
    end

    describe 'valuer field validations' do
      it { is_expected.to validate_length_of(:valuer_business_name).is_at_most(255) }
      it { is_expected.to validate_length_of(:valuer_email).is_at_most(255) }
      it { is_expected.to validate_length_of(:valuer_first_name).is_at_most(100) }
      it { is_expected.to validate_length_of(:valuer_last_name).is_at_most(100) }
      it { is_expected.to validate_length_of(:valuer_mobile_number).is_at_most(20) }

      describe 'conditional presence validations' do
        context 'when appraisal_valuer_id is present' do
          it 'does not require valuer fields' do
          offer = build(:appraisal_offer,
            appraisal_valuer_id: 1,
            valuer_first_name: nil,
            valuer_last_name: nil,
            valuer_email: nil,
            valuer_mobile_number: nil
          )
          expect(offer).to be_valid
          end
        end

        context 'when appraisal_valuer_id is not present' do
          it 'requires valuer_first_name' do
          offer = build(:appraisal_offer,
            appraisal_valuer_id: nil,
            valuer_first_name: nil
          )
          expect(offer).not_to be_valid
          expect(offer.errors[:valuer_first_name]).to include("can't be blank")
          end

          it 'requires valuer_last_name' do
          offer = build(:appraisal_offer,
            appraisal_valuer_id: nil,
            valuer_last_name: nil
          )
          expect(offer).not_to be_valid
          expect(offer.errors[:valuer_last_name]).to include("can't be blank")
          end

          it 'requires valuer_email' do
          offer = build(:appraisal_offer,
            appraisal_valuer_id: nil,
            valuer_email: nil
          )
          expect(offer).not_to be_valid
          expect(offer.errors[:valuer_email]).to include("can't be blank")
          end

          it 'requires valuer_mobile_number' do
          offer = build(:appraisal_offer,
            appraisal_valuer_id: nil,
            valuer_mobile_number: nil
          )
          expect(offer).not_to be_valid
          expect(offer.errors[:valuer_mobile_number]).to include("can't be blank")
          end
        end
      end
    end
  end
end
