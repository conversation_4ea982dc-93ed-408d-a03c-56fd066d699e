require 'rails_helper'

RSpec.describe Appraisal<PERSON>aluer, type: :model do
  describe "validations" do
    subject { build(:appraisal_valuer) }

    it { is_expected.to validate_presence_of(:business_name) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_presence_of(:first_name) }
    it { is_expected.to validate_presence_of(:last_name) }
    it { is_expected.to validate_presence_of(:mobile_number) }
    it { is_expected.to validate_presence_of(:status) }

    it { is_expected.to validate_length_of(:business_name).is_at_most(255) }
    it { is_expected.to validate_length_of(:email).is_at_most(255) }
    it { is_expected.to validate_length_of(:first_name).is_at_most(100) }
    it { is_expected.to validate_length_of(:last_name).is_at_most(100) }
    it { is_expected.to validate_length_of(:mobile_number).is_at_most(20) }

    it { is_expected.to validate_uniqueness_of(:email).scoped_to(:dealership_id).case_insensitive }

    describe "email format validation" do
      it "accepts valid email formats" do
        valuer = build(:appraisal_valuer, email: "<EMAIL>")
        expect(valuer).to be_valid
      end

      it "rejects invalid email formats" do
        valuer = build(:appraisal_valuer, email: "invalid-email")
        expect(valuer).not_to be_valid
        expect(valuer.errors[:email]).to include("invalid-email is not a valid email")
      end
    end
  end

  describe "associations" do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_many(:appraisal_offers).dependent(:destroy) }
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:status).with_values(active: 0, inactive: 1, deleted: 2).backed_by_column_of_type(:integer) }
  end

  describe "default values" do
    it "sets default status to active" do
      valuer = AppraisalValuer.new
      expect(valuer.status).to eq("active")
    end
  end

  describe "scopes" do
    let!(:active_valuer) { create(:appraisal_valuer, status: :active) }
    let!(:inactive_valuer) { create(:appraisal_valuer, status: :inactive) }
    let!(:deleted_valuer) { create(:appraisal_valuer, status: :deleted) }

    describe ".active" do
      it "returns only active valuers" do
        expect(AppraisalValuer.active).to include(active_valuer)
        expect(AppraisalValuer.active).not_to include(inactive_valuer)
        expect(AppraisalValuer.active).not_to include(deleted_valuer)
      end
    end

    describe ".inactive" do
      it "returns only inactive valuers" do
        expect(AppraisalValuer.inactive).to include(inactive_valuer)
        expect(AppraisalValuer.inactive).not_to include(active_valuer)
        expect(AppraisalValuer.inactive).not_to include(deleted_valuer)
      end
    end

    describe ".not_deleted" do
      it "returns active and inactive valuers but not deleted ones" do
        expect(AppraisalValuer.not_deleted).to include(active_valuer)
        expect(AppraisalValuer.not_deleted).to include(inactive_valuer)
        expect(AppraisalValuer.not_deleted).not_to include(deleted_valuer)
      end
    end
  end

  describe "instance methods" do
    describe "#full_name" do
      it "returns the full name" do
        valuer = build(:appraisal_valuer, first_name: "John", last_name: "Doe")
        expect(valuer.full_name).to eq("John Doe")
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:appraisal_valuer)).to be_valid
    end

    it "generates unique emails" do
      valuer1 = create(:appraisal_valuer)
      valuer2 = create(:appraisal_valuer)
      expect(valuer1.email).not_to eq(valuer2.email)
    end
  end

  describe "traits" do
    it "creates an inactive valuer" do
      valuer = create(:appraisal_valuer, :inactive)
      expect(valuer.status).to eq("inactive")
    end

    it "creates a deleted valuer" do
      valuer = create(:appraisal_valuer, :deleted)
      expect(valuer.status).to eq("deleted")
    end

    it "creates a valuer with specific email" do
      valuer = create(:appraisal_valuer, :with_specific_email)
      expect(valuer.email).to eq("<EMAIL>")
    end
  end

  describe "uniqueness constraints" do
    let(:dealership1) { create(:dealership) }
    let(:dealership2) { create(:dealership) }

    it "allows same email for different dealerships" do
      create(:appraisal_valuer, dealership: dealership1, email: "<EMAIL>")
      valuer2 = build(:appraisal_valuer, dealership: dealership2, email: "<EMAIL>")
      expect(valuer2).to be_valid
    end

    it "prevents duplicate emails within the same dealership" do
      create(:appraisal_valuer, dealership: dealership1, email: "<EMAIL>")
      valuer2 = build(:appraisal_valuer, dealership: dealership1, email: "<EMAIL>")
      expect(valuer2).not_to be_valid
      expect(valuer2.errors[:email]).to include("has already been taken")
    end

    it "is case insensitive for email uniqueness" do
      create(:appraisal_valuer, dealership: dealership1, email: "<EMAIL>")
      valuer2 = build(:appraisal_valuer, dealership: dealership1, email: "<EMAIL>")
      expect(valuer2).not_to be_valid
      expect(valuer2.errors[:email]).to include("has already been taken")
    end
  end
end
