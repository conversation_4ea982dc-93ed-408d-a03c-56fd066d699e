require 'rails_helper'

RSpec.describe Appraisal, type: :model do
  describe 'associations' do
    it { should belong_to(:dealership) }
    it { should belong_to(:customer) }
    it { should have_one(:customer_vehicle).dependent(:destroy) }
    it { should belong_to(:sales_person).class_name('User') }
    it { should belong_to(:created_by).class_name('User') }
    it { should belong_to(:updated_by).class_name('User') }
    it { should have_many(:favourite_appraisals).dependent(:destroy) }
    it { should have_many(:favourited_by_users).through(:favourite_appraisals).source(:user) }
    it { should have_one_attached(:customer_signature) }
    it { should have_many(:appraisal_offers).dependent(:destroy) }
  end

  describe 'validations' do
    it { should validate_numericality_of(:completed_percentage).is_greater_than_or_equal_to(0).is_less_than_or_equal_to(100).allow_nil }
    it { should validate_numericality_of(:awarded_value).allow_nil }
    it { should validate_numericality_of(:price).allow_nil }
    it { should validate_numericality_of(:given_price).allow_nil }
    it { should validate_length_of(:awarded_notes).is_at_most(1000).allow_blank }
  end

  it { should define_enum_for(:status).with_values(incomplete: 0, complete: 1, awarded: 2, archived: 3, deleted: 4).backed_by_column_of_type(:integer) }
  it { should define_enum_for(:previous_status).with_values(incomplete: 0, complete: 1, awarded: 2, archived: 3, deleted: 4).with_prefix(true).backed_by_column_of_type(:integer) }
  it { should define_enum_for(:appraisal_status).with_values(retail: 0, wholesale: 1, lost: 2).backed_by_column_of_type(:integer) }
  it { should have_one_attached(:customer_signature) }

  describe 'customer signature' do
    let(:appraisal) { create(:appraisal) }
    let(:signature_file) { fixture_file_upload('test_logo.png', 'image/png') }

    describe '#attach_customer_signature' do
      it 'attaches a signature file' do
        appraisal.attach_customer_signature(signature_file)
        expect(appraisal.customer_signature).to be_attached
      end

      it 'raises error when signature file is blank' do
        expect {
          appraisal.attach_customer_signature(nil)
        }.to raise_error(Errors::InvalidInput, "Signature file is required")
      end

      it 'purges existing signature before attaching new one' do
        # First attach a signature
        appraisal.attach_customer_signature(signature_file)
        expect(appraisal.customer_signature).to be_attached

        # Attach another signature
        new_signature = fixture_file_upload('profile.jpg', 'image/jpeg')
        expect(appraisal.customer_signature).to receive(:purge)
        appraisal.attach_customer_signature(new_signature)
      end
    end

    describe '#customer_signature_url' do
      it 'returns nil when no signature is attached' do
        expect(appraisal.customer_signature_url).to be_nil
      end

      it 'returns URL when signature is attached' do
        appraisal.attach_customer_signature(signature_file)
        expect(appraisal.customer_signature_url).to be_present
        expect(appraisal.customer_signature_url).to include('test_logo.png')
      end
    end
  end

  describe 'scopes' do
    let(:dealership) { create(:dealership, :without_brand) }
    let(:customer) { create(:customer, dealership: dealership) }
    let(:sales_person) { create(:user) }
    let(:brand) { create(:brand, name: "Test Brand #{SecureRandom.hex(4)}") }
    let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :complete) }
    let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, brand: brand, rego: 'ABC123') }
    let!(:deleted_appraisal) { create(:appraisal, dealership: dealership, customer: customer, status: :deleted, sales_person: sales_person) }

    it 'excludes deleted appraisals by default' do
      expect(Appraisal.all).to include(appraisal)
      expect(Appraisal.all).not_to include(deleted_appraisal)
    end

    it 'filters by status' do
      expect(Appraisal.filter_by_status(:complete)).to include(appraisal)
    end

    it 'filters by customer' do
      expect(Appraisal.by_customer(customer.uuid)).to include(appraisal)
    end

    it 'filters by brand' do
      expect(Appraisal.by_brand(brand.uuid)).to include(appraisal)
    end

    it 'filters by salesperson' do
      expect(Appraisal.by_salesperson(sales_person.uuid)).to include(appraisal)
    end

    it 'filters by registration number' do
      expect(Appraisal.by_registration_number('ABC123')).to include(appraisal)
    end

    describe 'created_between_dates' do
      it 'filters by start date' do
        start_date = 1.day.ago.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(start_date, nil)).to include(appraisal)
      end

      it 'filters by end date' do
        end_date = 1.day.from_now.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(nil, end_date)).to include(appraisal)
      end

      it 'filters by date range' do
        start_date = 1.day.ago.strftime('%Y-%m-%d')
        end_date = 1.day.from_now.strftime('%Y-%m-%d')
        expect(Appraisal.created_between_dates(start_date, end_date)).to include(appraisal)
      end

      it 'raises error for invalid start date' do
        expect { Appraisal.created_between_dates('invalid-date', nil) }.to raise_error(Errors::InvalidInput, /Invalid date format/)
      end

      it 'raises error for invalid end date' do
        expect { Appraisal.created_between_dates(nil, 'invalid-date') }.to raise_error(Errors::InvalidInput, /Invalid date format/)
      end
    end

    it 'searches by customer name' do
      expect(Appraisal.search_by_term(customer.first_name.downcase)).to include(appraisal)
    end

    it 'searches by customer email' do
      expect(Appraisal.search_by_term(customer.email)).to include(appraisal)
    end

    it 'searches by customer full name' do
      expect(Appraisal.search_by_term("#{customer.last_name} #{customer.first_name}")).to include(appraisal)
    end

    it 'searches by customer phone number' do
      customer.update!(phone_number: '+61412345678')
      expect(Appraisal.search_by_term('412345678')).to include(appraisal)
    end

    it 'searches by vehicle build year' do
      expect(Appraisal.search_by_term(customer_vehicle.build_year.to_s)).to include(appraisal)
    end

    it 'rejects short search terms' do
      expect(Appraisal.search_by_term('ab')).to be_empty
    end
  end

  describe '#favourited_by?' do
    let(:user) { create(:user) }
    let(:other_user) { create(:user) }
    let(:appraisal) { create(:appraisal) }

    context 'when user has favourited the appraisal' do
      before { create(:favourite_appraisal, user: user, appraisal: appraisal) }

      it 'returns true' do
        expect(appraisal.favourited_by?(user)).to be true
      end
    end

    context 'when user has not favourited the appraisal' do
      it 'returns false' do
        expect(appraisal.favourited_by?(user)).to be false
      end
    end

    context 'when other user has favourited the appraisal' do
      before { create(:favourite_appraisal, user: other_user, appraisal: appraisal) }

      it 'returns false for the current user' do
        expect(appraisal.favourited_by?(user)).to be false
      end
    end

    context 'when user is nil' do
      it 'returns false' do
        expect(appraisal.favourited_by?(nil)).to be false
      end
    end
  end

  describe 'archive/unarchive functionality' do
    let(:appraisal) { create(:appraisal, status: :complete) }

    describe 'archiving' do
      it 'stores the previous status when archiving' do
        expect {
          appraisal.update!(status: :archived, previous_status: appraisal.status)
        }.to change { appraisal.previous_status }.from(nil).to('complete')
      end

      it 'allows archiving from any status' do
        %w[incomplete complete awarded].each do |status|
          appraisal = create(:appraisal, status: status)
          expect {
            appraisal.update!(status: :archived, previous_status: appraisal.status)
          }.to change { appraisal.status }.to('archived')
        end
      end
    end

    describe 'unarchiving' do
      let(:archived_appraisal) { create(:appraisal, status: :archived, previous_status: :complete) }

      it 'restores the previous status when unarchiving' do
        expect {
          archived_appraisal.update!(status: archived_appraisal.previous_status, previous_status: nil)
        }.to change { archived_appraisal.status }.from('archived').to('complete')
      end

      it 'clears the previous status when unarchiving' do
        expect {
          archived_appraisal.update!(status: archived_appraisal.previous_status, previous_status: nil)
        }.to change { archived_appraisal.previous_status }.from('complete').to(nil)
      end
    end

    describe 'previous_status enum helpers' do
      let(:appraisal) { create(:appraisal, previous_status: :complete) }

      it 'provides prefixed enum helpers' do
        expect(appraisal.previous_status_complete?).to be true
        expect(appraisal.previous_status_incomplete?).to be false
        expect(appraisal.previous_status_awarded?).to be false
      end
    end
  end
end
