require 'rails_helper'

RSpec.describe Vehicle::OptionsFitted, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:customer_vehicle) }
  end

    describe 'validations' do
    subject { build(:options_fitted) }
    describe 'extended_warranty_expiry validation' do
      context 'when has_extended_warranty is true' do
        it 'requires extended_warranty_expiry to be present' do
          customer_vehicle = create(:customer_vehicle)
          options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, has_extended_warranty: true, extended_warranty_expiry: nil)
          expect(options_fitted).not_to be_valid
          expect(options_fitted.errors[:extended_warranty_expiry]).to include("can't be blank")
        end

        it 'is valid when extended_warranty_expiry is present' do
          customer_vehicle = create(:customer_vehicle)
          options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, has_extended_warranty: true, extended_warranty_expiry: 1.year.from_now.to_date)
          expect(options_fitted).to be_valid
        end
      end

      context 'when has_extended_warranty is false' do
        it 'allows extended_warranty_expiry to be nil' do
          customer_vehicle = create(:customer_vehicle)
          options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, has_extended_warranty: false, extended_warranty_expiry: nil)
          expect(options_fitted).to be_valid
        end

        it 'allows extended_warranty_expiry to be present' do
          customer_vehicle = create(:customer_vehicle)
          options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, has_extended_warranty: false, extended_warranty_expiry: 1.year.from_now.to_date)
          expect(options_fitted).to be_valid
        end
      end
    end

    describe 'notes validation' do
      it 'allows notes to be nil' do
        customer_vehicle = create(:customer_vehicle)
        options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, notes: nil)
        expect(options_fitted).to be_valid
      end

      it 'allows notes to be blank' do
        customer_vehicle = create(:customer_vehicle)
        options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, notes: '')
        expect(options_fitted).to be_valid
      end

      it 'allows notes up to 1000 characters' do
        customer_vehicle = create(:customer_vehicle)
        notes = 'a' * 1000
        options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, notes: notes)
        expect(options_fitted).to be_valid
      end

      it 'rejects notes longer than 1000 characters' do
        customer_vehicle = create(:customer_vehicle)
        notes = 'a' * 1001
        options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, notes: notes)
        expect(options_fitted).not_to be_valid
        expect(options_fitted.errors[:notes]).to include('is too long (maximum is 1000 characters)')
      end
    end
  end

  describe 'scopes' do
    let!(:with_extended_warranty) { create(:options_fitted, :with_extended_warranty) }
    let!(:without_extended_warranty) { create(:options_fitted, has_extended_warranty: false) }
    let!(:with_ppsr) { create(:options_fitted, :with_ppsr) }
    let!(:without_ppsr) { create(:options_fitted, ppsr: false) }

    describe '.with_extended_warranty' do
      it 'returns only records with extended warranty' do
        expect(Vehicle::OptionsFitted.with_extended_warranty).to include(with_extended_warranty)
        expect(Vehicle::OptionsFitted.with_extended_warranty).not_to include(without_extended_warranty)
      end
    end

    describe '.with_ppsr' do
      it 'returns only records with PPSR' do
        expect(Vehicle::OptionsFitted.with_ppsr).to include(with_ppsr)
        expect(Vehicle::OptionsFitted.with_ppsr).not_to include(without_ppsr)
      end
    end
  end

  describe 'concerns' do
    it 'includes HasUuid' do
      expect(Vehicle::OptionsFitted.included_modules).to include(HasUuid)
    end
  end

  describe 'uuid generation' do
    it 'generates a uuid on creation' do
      options_fitted = create(:options_fitted)
      expect(options_fitted.uuid).to be_present
      expect(options_fitted.uuid).to match(/\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/)
    end

    it 'does not change uuid on update' do
      options_fitted = create(:options_fitted)
      original_uuid = options_fitted.uuid
      options_fitted.update!(has_sunroof: true)
      expect(options_fitted.uuid).to eq(original_uuid)
    end

    it 'enforces uniqueness of uuid' do
      existing_options = create(:options_fitted)
      new_options = build(:options_fitted, uuid: existing_options.uuid)
      expect(new_options).not_to be_valid
      expect(new_options.errors[:uuid]).to include("has already been taken")
    end
  end

  describe 'factory' do
    it 'has a valid factory' do
      customer_vehicle = create(:customer_vehicle)
      options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle)
      expect(options_fitted).to be_valid
    end

    it 'creates with extended warranty trait' do
      options_fitted = create(:options_fitted, :with_extended_warranty)
      expect(options_fitted.has_extended_warranty).to be true
      expect(options_fitted.extended_warranty_expiry).to be_present
    end

    it 'creates with PPSR trait' do
      options_fitted = create(:options_fitted, :with_ppsr)
      expect(options_fitted.ppsr).to be true
    end

    it 'creates with sunroof trait' do
      options_fitted = create(:options_fitted, :with_sunroof)
      expect(options_fitted.has_sunroof).to be true
    end

    it 'creates with tinted windows trait' do
      options_fitted = create(:options_fitted, :with_tinted_windows)
      expect(options_fitted.has_tinted_windows).to be true
    end

    it 'creates with towbar trait' do
      options_fitted = create(:options_fitted, :with_towbar)
      expect(options_fitted.has_towbar).to be true
    end

    it 'creates with keyless entry trait' do
      options_fitted = create(:options_fitted, :with_keyless_entry)
      expect(options_fitted.has_keyless_entry).to be true
    end

    it 'creates with bluetooth trait' do
      options_fitted = create(:options_fitted, :with_bluetooth)
      expect(options_fitted.has_bluetooth).to be true
    end

    it 'creates with ventilated seats trait' do
      options_fitted = create(:options_fitted, :with_ventilated_seats)
      expect(options_fitted.has_ventilated_seats).to be true
    end

    it 'creates with tray fitted trait' do
      options_fitted = create(:options_fitted, :with_tray_fitted)
      expect(options_fitted.has_tray_fitted).to be true
    end

    it 'creates with canopy fitted trait' do
      options_fitted = create(:options_fitted, :with_canopy_fitted)
      expect(options_fitted.has_canopy_fitted).to be true
    end

    it 'creates with aftermarket wheels trait' do
      options_fitted = create(:options_fitted, :with_aftermarket_wheels)
      expect(options_fitted.has_aftermarket_wheels).to be true
    end

    it 'creates with bull bar trait' do
      options_fitted = create(:options_fitted, :with_bull_bar)
      expect(options_fitted.has_bull_bar).to be true
    end

    it 'creates with additional options trait' do
      options_fitted = create(:options_fitted, :with_additional_options)
      expect(options_fitted.additional_options).to include("custom_paint" => "Metallic Blue")
      expect(options_fitted.additional_options).to include("performance_exhaust" => true)
    end

    it 'creates fully loaded trait' do
      options_fitted = create(:options_fitted, :fully_loaded)
      expect(options_fitted.has_sunroof).to be true
      expect(options_fitted.has_tinted_windows).to be true
      expect(options_fitted.has_towbar).to be true
      expect(options_fitted.has_keyless_entry).to be true
      expect(options_fitted.has_bluetooth).to be true
      expect(options_fitted.has_ventilated_seats).to be true
      expect(options_fitted.has_tray_fitted).to be true
      expect(options_fitted.has_canopy_fitted).to be true
      expect(options_fitted.has_aftermarket_wheels).to be true
      expect(options_fitted.has_bull_bar).to be true
      expect(options_fitted.has_extended_warranty).to be true
      expect(options_fitted.ppsr).to be true
      expect(options_fitted.extended_warranty_expiry).to be_present
      expect(options_fitted.additional_options).to include("premium_sound_system" => true)
      expect(options_fitted.notes).to eq("Fully loaded vehicle with all premium options and custom modifications")
    end

    it 'creates expired warranty trait' do
      options_fitted = create(:options_fitted, :expired_warranty)
      expect(options_fitted.has_extended_warranty).to be true
      expect(options_fitted.extended_warranty_expiry).to be < Date.current
    end

    it 'creates expiring warranty trait' do
      options_fitted = create(:options_fitted, :expiring_warranty)
      expect(options_fitted.has_extended_warranty).to be true
      expect(options_fitted.extended_warranty_expiry).to be > Date.current
      expect(options_fitted.extended_warranty_expiry).to be < 2.months.from_now.to_date
    end

    it 'creates with notes trait' do
      options_fitted = create(:options_fitted, :with_notes)
      expect(options_fitted.notes).to eq("Vehicle has premium sound system and custom modifications")
    end
  end

  describe 'boolean fields' do
    let(:options_fitted) { build(:options_fitted) }

    it 'defaults all boolean fields to false' do
      expect(options_fitted.has_sunroof).to be false
      expect(options_fitted.has_tinted_windows).to be false
      expect(options_fitted.has_towbar).to be false
      expect(options_fitted.has_keyless_entry).to be false
      expect(options_fitted.has_bluetooth).to be false
      expect(options_fitted.has_ventilated_seats).to be false
      expect(options_fitted.has_tray_fitted).to be false
      expect(options_fitted.has_canopy_fitted).to be false
      expect(options_fitted.has_aftermarket_wheels).to be false
      expect(options_fitted.has_bull_bar).to be false
      expect(options_fitted.has_extended_warranty).to be false
      expect(options_fitted.ppsr).to be false
    end

    it 'allows boolean fields to be set to true' do
      customer_vehicle = create(:customer_vehicle)
      options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle)

      options_fitted.has_sunroof = true
      options_fitted.has_tinted_windows = true
      options_fitted.has_towbar = true
      options_fitted.has_keyless_entry = true
      options_fitted.has_bluetooth = true
      options_fitted.has_ventilated_seats = true
      options_fitted.has_tray_fitted = true
      options_fitted.has_canopy_fitted = true
      options_fitted.has_aftermarket_wheels = true
      options_fitted.has_bull_bar = true
      options_fitted.has_extended_warranty = true
      options_fitted.extended_warranty_expiry = 1.year.from_now.to_date
      options_fitted.ppsr = true

      expect(options_fitted).to be_valid
    end
  end

  describe 'additional_options field' do
    it 'accepts a hash of additional options' do
      customer_vehicle = create(:customer_vehicle)
      options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, additional_options: {
        "custom_paint" => "Red",
        "performance_exhaust" => true,
        "sports_suspension" => true
      })
      expect(options_fitted).to be_valid
    end

    it 'accepts an empty hash' do
      customer_vehicle = create(:customer_vehicle)
      options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, additional_options: {})
      expect(options_fitted).to be_valid
    end

    it 'accepts nil' do
      customer_vehicle = create(:customer_vehicle)
      options_fitted = build(:options_fitted, customer_vehicle: customer_vehicle, additional_options: nil)
      expect(options_fitted).to be_valid
    end
  end

  describe 'new fields' do
    let(:options_fitted) { build(:options_fitted) }

    it 'accepts valid sunroof_type enum values' do
      expect(build(:options_fitted, sunroof_type: :standard_metal)).to be_valid
      expect(build(:options_fitted, sunroof_type: :standard_glass)).to be_valid
      expect(build(:options_fitted, sunroof_type: :panoramic)).to be_valid
    end

    it 'accepts valid tonneau_type enum values' do
      expect(build(:options_fitted, tonneau_type: :hard)).to be_valid
      expect(build(:options_fitted, tonneau_type: :soft)).to be_valid
    end

    it 'accepts valid on_written_off_register enum values' do
      expect(build(:options_fitted, on_written_off_register: :yes)).to be_valid
      expect(build(:options_fitted, on_written_off_register: :no)).to be_valid
      expect(build(:options_fitted, on_written_off_register: :unknown)).to be_valid
    end

    it 'validates number_of_keys inclusion' do
      expect(build(:options_fitted, number_of_keys: 1)).to be_valid
      expect(build(:options_fitted, number_of_keys: 2)).to be_valid
      expect(build(:options_fitted, number_of_keys: 3)).to be_valid
      expect(build(:options_fitted, number_of_keys: 4)).not_to be_valid
      expect(build(:options_fitted, number_of_keys: 0)).not_to be_valid
    end

    it 'accepts boolean values for heated_seats, cargo_blind, tonneau_cover' do
      expect(build(:options_fitted, heated_seats: true)).to be_valid
      expect(build(:options_fitted, heated_seats: false)).to be_valid
      expect(build(:options_fitted, cargo_blind: true)).to be_valid
      expect(build(:options_fitted, cargo_blind: false)).to be_valid
      expect(build(:options_fitted, tonneau_cover: true)).to be_valid
      expect(build(:options_fitted, tonneau_cover: false)).to be_valid
    end

    it 'accepts a date for last_ppsr_date' do
      expect(build(:options_fitted, last_ppsr_date: Time.zone.today)).to be_valid
      expect(build(:options_fitted, last_ppsr_date: nil)).to be_valid
    end

    it 'accepts text for notes' do
      expect(build(:options_fitted, notes: "Some notes about the vehicle")).to be_valid
      expect(build(:options_fitted, notes: nil)).to be_valid
      expect(build(:options_fitted, notes: "")).to be_valid
    end

    it 'can attach options_images' do
      options_fitted = build(:options_fitted)
      options_fitted.options_images.attach(
        io: StringIO.new('fake image data'),
        filename: 'test.jpg',
        content_type: 'image/jpeg'
      )
      expect(options_fitted.options_images).to be_attached
    end

    it 'validates options_images content type' do
      options_fitted = build(:options_fitted)
      options_fitted.options_images.attach(
        io: StringIO.new('fake image data'),
        filename: 'test.txt',
        content_type: 'text/plain'
      )
      expect(options_fitted).not_to be_valid
      expect(options_fitted.errors[:options_images]).to include('has an invalid content type (authorized content types are PNG, JPG)')
    end

    it 'validates options_images file size' do
      options_fitted = build(:options_fitted)
      # Create a file larger than 5MB
      large_file = StringIO.new('x' * (6 * 1024 * 1024))
      options_fitted.options_images.attach(
        io: large_file,
        filename: 'large.jpg',
        content_type: 'image/jpeg'
      )
      expect(options_fitted).not_to be_valid
      expect(options_fitted.errors[:options_images]).to include('file size must be less than 5 MB (current size is 6 MB)')
    end

    it 'validates options_images limit' do
      options_fitted = build(:options_fitted)
      # Attach 6 images (more than the limit of 5)
      6.times do |i|
        options_fitted.options_images.attach(
          io: StringIO.new('fake image data'),
          filename: "test#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(options_fitted).not_to be_valid
      expect(options_fitted.errors[:options_images]).to include('too many files attached (maximum is 5 files, got 6)')
    end
  end

  describe 'dependent field clearing' do
    let(:customer_vehicle) { create(:customer_vehicle) }

    context 'when has_sunroof is set to false' do
      it 'clears sunroof_type' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle, has_sunroof: true, sunroof_type: :panoramic)

        options_fitted.update!(has_sunroof: false)

        expect(options_fitted.sunroof_type).to be_nil
      end
    end

    context 'when tonneau_cover is set to false' do
      it 'clears tonneau_type' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle, tonneau_cover: true, tonneau_type: :hard)

        options_fitted.update!(tonneau_cover: false)

        expect(options_fitted.tonneau_type).to be_nil
      end
    end

    context 'when has_extended_warranty is set to false' do
      it 'clears extended_warranty_expiry' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle, has_extended_warranty: true, extended_warranty_expiry: 1.year.from_now.to_date)

        options_fitted.update!(has_extended_warranty: false)

        expect(options_fitted.extended_warranty_expiry).to be_nil
      end
    end

    context 'when ppsr is set to false' do
      it 'clears last_ppsr_date' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle, ppsr: true, last_ppsr_date: Date.current)

        options_fitted.update!(ppsr: false)

        expect(options_fitted.last_ppsr_date).to be_nil
      end
    end

    context 'when boolean fields are set to true' do
      it 'does not clear dependent fields' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle,
                               has_sunroof: false, sunroof_type: :panoramic,
                               tonneau_cover: false, tonneau_type: :hard,
                               has_extended_warranty: false, extended_warranty_expiry: 1.year.from_now.to_date,
                               ppsr: false, last_ppsr_date: Date.current)

        options_fitted.update!(has_sunroof: true, tonneau_cover: true, has_extended_warranty: true, ppsr: true, extended_warranty_expiry: 1.year.from_now.to_date)

        expect(options_fitted.sunroof_type).to eq('panoramic')
        expect(options_fitted.tonneau_type).to eq('hard')
        expect(options_fitted.extended_warranty_expiry).to be_present
        expect(options_fitted.last_ppsr_date).to be_present
      end
    end

    context 'when multiple boolean fields are set to false simultaneously' do
      it 'clears all corresponding dependent fields' do
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle,
                               has_sunroof: true, sunroof_type: :panoramic,
                               tonneau_cover: true, tonneau_type: :hard,
                               has_extended_warranty: true, extended_warranty_expiry: 1.year.from_now.to_date,
                               ppsr: true, last_ppsr_date: Date.current)

        options_fitted.update!(has_sunroof: false, tonneau_cover: false, has_extended_warranty: false, ppsr: false)

        expect(options_fitted.sunroof_type).to be_nil
        expect(options_fitted.tonneau_type).to be_nil
        expect(options_fitted.extended_warranty_expiry).to be_nil
        expect(options_fitted.last_ppsr_date).to be_nil
      end
    end
  end

  describe 'database constraints' do
    it 'enforces uuid uniqueness at the database level' do
      existing_options = create(:options_fitted)
      customer_vehicle = create(:customer_vehicle)
      duplicate = build(:options_fitted, customer_vehicle: customer_vehicle, uuid: existing_options.uuid)

      expect {
        duplicate.save(validate: false)
      }.to raise_error(ActiveRecord::RecordNotUnique)
    end
  end
end
