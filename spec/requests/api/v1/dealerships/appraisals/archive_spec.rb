# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/archive", type: :request do
  include_context "users_api_shared_context"

  let(:test_brand) { create(:brand) }
  let(:dealership) { create(:dealership, brand: test_brand) }
  let(:dealership_uuid) { dealership.uuid }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin) }
  let(:customer) { create(:customer, dealership: dealership) }
  let(:sales_person) { create(:user) }
  let(:sales_person_uuid) { sales_person.uuid }
  let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
  let(:customer_vehicle) { create(:customer_vehicle, customer: customer, brand: test_brand, rego: 'ABC123TEST', make: 'Toyota', model: 'Camry') }
  let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, customer_vehicle: customer_vehicle, sales_person: sales_person, status: :complete) }

  # Regular RSpec tests
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/archive" do
    context "archiving an appraisal" do
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", params: { status: true }, headers: headers }

      context "with valid authentication" do
        it "marks the appraisal as archived" do
          expect { subject }.to change { appraisal.reload.status }.from('complete').to('archived')

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig('status', 'code')).to eq(200)
          expect(json.dig('status', 'message')).to eq('Appraisal archived successfully')
        end

        it "stores the previous status" do
          expect { subject }.to change { appraisal.reload.previous_status }.from(nil).to('complete')
        end

        it "updates the updated_by field" do
          expect { subject }.to change { appraisal.reload.updated_by_id }.to(user.id)
        end

        it "does not physically delete the record" do
          expect { subject }.not_to change { Appraisal.unscoped.count }
        end

        it "marks the appraisal status as archived" do
          subject
          appraisal.reload
          expect(appraisal.status).to eq('archived')
        end

        it "returns the updated appraisal data" do
          subject
          json = response.parsed_body
          expect(json.dig('data', 'appraisal')).to be_present
          expect(json.dig('data', 'appraisal', 'uuid')).to eq(appraisal.uuid)
          expect(json.dig('data', 'appraisal', 'status')).to eq('archived')
        end
      end

      context "when appraisal is already archived" do
        before { appraisal.update!(status: :archived, previous_status: :complete) }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Appraisal is already archived")
        end
      end
    end

    context "unarchiving an appraisal" do
      let!(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived, previous_status: :complete) }
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{archived_appraisal.uuid}/archive", params: { status: false }, headers: headers }

      context "with valid authentication" do
        it "restores the appraisal to previous status" do
          expect { subject }.to change { archived_appraisal.reload.status }.from('archived').to('complete')

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig('status', 'code')).to eq(200)
          expect(json.dig('status', 'message')).to eq('Appraisal unarchived successfully')
        end

        it "clears the previous status" do
          expect { subject }.to change { archived_appraisal.reload.previous_status }.from('complete').to(nil)
        end

        it "updates the updated_by field" do
          expect { subject }.to change { archived_appraisal.reload.updated_by_id }.to(user.id)
        end

        it "returns the updated appraisal data" do
          subject
          json = response.parsed_body
          expect(json.dig('data', 'appraisal')).to be_present
          expect(json.dig('data', 'appraisal', 'uuid')).to eq(archived_appraisal.uuid)
          expect(json.dig('data', 'appraisal', 'status')).to eq('complete')
        end
      end

      context "when appraisal is not archived" do
        subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", params: { status: false }, headers: headers }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Appraisal is not archived")
        end
      end

      context "when previous status is missing" do
        let!(:archived_appraisal_no_previous) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived, previous_status: nil) }
        subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{archived_appraisal_no_previous.uuid}/archive", params: { status: false }, headers: headers }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Cannot unarchive: previous status not found")
        end
      end
    end

    context "missing status parameter" do
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", headers: headers }

      it "returns validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        json = response.parsed_body
        expect(json.dig("status", "code")).to eq(422)
        expect(json.dig("status", "message")).to include("status parameter is required")
      end
    end

    context "invalid status parameter" do
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", params: { status: "invalid" }, headers: headers }

      it "returns validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        json = response.parsed_body
        expect(json.dig("status", "code")).to eq(422)
        expect(json.dig("status", "message")).to include("status must be true or false")
      end
    end

    context "with deleted appraisal" do
      before { appraisal.update!(status: :deleted) }
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", params: { status: true }, headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context "with non-existent appraisal" do
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/non-existent-uuid/archive", params: { status: true }, headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context "with invalid dealership" do
      subject { put "/api/v1/dealerships/invalid-uuid/appraisals/#{appraisal.uuid}/archive", params: { status: true }, headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", params: { status: true }, headers: headers }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger Documentation
  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/archive" do
    put "Archive or unarchive an appraisal" do
      tags "Appraisals"
      description "Changes the appraisal status to archived or restores it from archived state. When archiving, the previous status is stored. When unarchiving, the previous status is restored."
      operationId "archiveAppraisal"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "dealership_uuid", in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: "appraisal_uuid", in: :path, type: :string, required: true, description: "Appraisal UUID"
      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :status, in: :query, type: :boolean, required: true, description: "true to archive, false to unarchive"

      response "200", "Appraisal archived or unarchived successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisal archived successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'archived' ], example: 'archived' },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:status) { true }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Appraisal archived successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "status")).to eq("archived")
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          # Verify the appraisal is marked as archived
          appraisal.reload
          expect(appraisal.status).to eq('archived')
        end
      end

      response "200", "Appraisal unarchived successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisal unarchived successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'awarded' ], example: 'complete' }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let!(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived, previous_status: :complete) }
        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:status) { false }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Appraisal unarchived successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "status")).to eq("complete")
          expect(json.dig("data", "appraisal", "uuid")).to eq(archived_appraisal.uuid)

          # Verify the appraisal is unarchived
          archived_appraisal.reload
          expect(archived_appraisal.status).to eq('complete')
          expect(archived_appraisal.previous_status).to be_nil
        end
      end

      response "422", "Validation error" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: 'status parameter is required' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        context "when status parameter is missing" do
          let(:appraisal_uuid) { appraisal.uuid }

          before do
            put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/archive", headers: headers
          end

          it "returns a 422 response" do
            json = response.parsed_body
            expect(response.status).to eq(422)
            expect(json.dig("status", "code")).to eq(422)
            expect(json.dig("status", "message")).to include("status parameter is required")
          end
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Appraisal not found or does not belong to this dealership' }
                   },
                   required: [ 'code', 'message' ]
                 }
               }

        context "when appraisal does not exist" do
          let(:appraisal_uuid) { "non-existent-uuid" }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "code")).to eq(404)
            expect(json.dig("status", "message")).to include("Appraisal not found")
          end
        end
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Unauthorized' }
                   },
                   required: [ 'code', 'message' ]
                 }
               }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:Authorization) { "Bearer invalid-token" }

        run_test!
      end
    end
  end
end
