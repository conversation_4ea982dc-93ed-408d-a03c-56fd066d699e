# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::AppraisalsController#update_customer", type: :request do
  # Swagger API Documentation Tests
  include_context "appraisal_api_shared_context"

  let(:customer_vehicle) { create(:customer_vehicle, customer: customer, brand: toyota, rego: 'ABC123', make: 'Toyota', model: 'Camry') }
  let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, customer_vehicle: customer_vehicle, sales_person: sales_person, status: :complete, completed_percentage: 85, awarded_value: 25000, price: 30000, notes: 'Test notes') }
  let(:appraisal_uuid) { appraisal.uuid }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/customer" do
    put "Update customer association in appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]
      description "Update customer association within an appraisal by providing an existing customer UUID. This also updates the customer reference in the associated customer vehicle."

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :customer_data, in: :body, schema: {
        type: :object,
        required: [ :customer_uuid ],
        properties: {
          customer_uuid: {
            type: :string,
            format: :uuid,
            example: "550e8400-e29b-41d4-a716-************",
            description: "UUID of the existing customer to associate with this appraisal"
          }
        }
      }

      response "200", "Customer association updated successfully" do
        let!(:new_customer) { create(:customer, dealership: dealership, first_name: "New", last_name: "Customer", email: "<EMAIL>") }
        let(:customer_data) { { customer_uuid: new_customer.uuid } }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Customer updated successfully")
          expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(new_customer.uuid)
          expect(json.dig("data", "appraisal", "customer", "first_name")).to eq("New")
          expect(json.dig("data", "appraisal", "customer", "last_name")).to eq("Customer")
          expect(json.dig("data", "appraisal", "customer", "email")).to eq("<EMAIL>")

          # Verify customer_vehicle customer reference is updated
          appraisal.customer_vehicle.reload
          expect(appraisal.customer_vehicle.customer.uuid).to eq(new_customer.uuid)
          expect(appraisal.customer_vehicle.customer.first_name).to eq("New")
        end
      end

      response "422", "Invalid input" do
        context "when customer_uuid not provided" do
          let(:customer_data) { {} }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to include("customer_uuid is required")
          end
        end

        context "when customer_uuid is blank" do
          let(:customer_data) { { customer_uuid: "" } }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to include("customer_uuid is required")
          end
        end
      end

      response "401", "Unauthorized" do
        let!(:new_customer) { create(:customer, dealership: dealership) }
        let(:customer_data) { { customer_uuid: new_customer.uuid } }
        let(:Authorization) { "Bearer invalid-token" }

        run_test!
      end

      response "404", "Appraisal not found" do
        let(:appraisal_uuid) { "non-existent-uuid" }
        let!(:new_customer) { create(:customer, dealership: dealership) }
        let(:customer_data) { { customer_uuid: new_customer.uuid } }

        run_test!
      end

      response "404", "Customer not found" do
        let(:customer_data) { { customer_uuid: "non-existent-uuid" } }

        run_test!
      end
    end
  end

  # Comprehensive Functional Tests
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/customer" do
    let(:customer_vehicle) { create(:customer_vehicle, customer: customer, brand: toyota, rego: 'ABC123', make: 'Toyota', model: 'Camry') }
    let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, customer_vehicle: customer_vehicle, sales_person: sales_person, status: :complete, completed_percentage: 85, awarded_value: 25000, price: 30000, notes: 'Test notes') }

    subject { put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/customer", params: params, headers: headers, as: :json }

    context "with valid customer_uuid" do
      let!(:new_customer) { create(:customer, dealership: dealership, first_name: "New", last_name: "Customer", email: "<EMAIL>") }
      let(:params) { { customer_uuid: new_customer.uuid } }

      it "updates the customer association successfully" do
        expect { subject }.not_to change(Customer, :count)

        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(new_customer.uuid)
        expect(json.dig("data", "appraisal", "customer", "first_name")).to eq("New")
        expect(json.dig("data", "appraisal", "customer", "last_name")).to eq("Customer")
        expect(json.dig("data", "appraisal", "customer", "email")).to eq("<EMAIL>")
      end

      it "updates the customer_vehicle customer reference" do
        subject

        customer_vehicle.reload
        expect(customer_vehicle.customer.uuid).to eq(new_customer.uuid)
        expect(customer_vehicle.customer.first_name).to eq("New")
        expect(customer_vehicle.customer.email).to eq("<EMAIL>")
      end
    end

    context "when appraisal has no customer_vehicle" do
      let!(:appraisal_without_vehicle) { create(:appraisal, dealership: dealership, customer: customer, sales_person: user) }
      let!(:another_customer) { create(:customer, dealership: dealership, first_name: "Another", last_name: "Customer") }
      let(:params) { { customer_uuid: another_customer.uuid } }

      it "updates customer successfully without affecting customer_vehicle" do
        put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal_without_vehicle.uuid}/customer", params: params, headers: headers, as: :json

        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Customer updated successfully")
        expect(json.dig("data", "appraisal", "customer", "uuid")).to eq(another_customer.uuid)
        expect(json.dig("data", "appraisal", "customer", "first_name")).to eq("Another")
      end
    end

    context "with invalid parameters" do
      context "when customer_uuid not provided" do
        let(:params) { {} }

        it "returns validation error" do
          subject

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body

          expect(json.dig("status", "message")).to include("customer_uuid is required")
        end
      end

      context "when customer_uuid is blank" do
        let(:params) { { customer_uuid: "" } }

        it "returns validation error" do
          subject

          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body

          expect(json.dig("status", "message")).to include("customer_uuid is required")
        end
      end

      context "when customer does not exist" do
        let(:params) { { customer_uuid: "non-existent-uuid" } }

        it "returns not found error" do
          subject

          expect(response).to have_http_status(:not_found)
          json = response.parsed_body

          expect(json.dig("status", "message")).to include("not found")
        end
      end
    end
  end
end
