# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-condition", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-condition" do
    put "Upsert vehicle condition for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :condition, in: :formData, schema: {
        type: :object,
        properties: {
          is_clean: { type: :boolean, description: "Is the vehicle clean", example: true },
          is_wet: { type: :boolean, description: "Is the vehicle wet", example: false },
          is_road_tested: { type: :boolean, description: "Has the vehicle been road tested", example: true },
          has_signs_of_repair: { type: :boolean, description: "Does the vehicle have signs of repair", example: false },
          repair_details: { type: :string, description: "Details about repairs if any", example: "No previous repairs detected" },
          additional_notes: { type: :string, description: "Additional notes about the vehicle condition", example: "Vehicle is in excellent condition" },
          photos: { type: :array, items: { type: :string, format: :binary }, description: "Condition photos (max 5)", maxItems: 5 }
        }
      }

      response "201", "Vehicle condition created successfully" do
        let(:appraisal_uuid) { appraisal.uuid }
        let(:condition) do
          {
            is_clean: true,
            is_wet: false,
            is_road_tested: true,
            has_signs_of_repair: false,
            repair_details: "No previous repairs detected",
            additional_notes: "Vehicle is in excellent condition"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'code')).to eq(201)
          expect(data.dig('status', 'message')).to eq('Vehicle condition created successfully')
          expect(data.dig('data', 'appraisal', 'vehicle', 'vehicle_condition')).to be_present
        end
      end

      response "200", "Vehicle condition updated successfully" do
        let(:appraisal_uuid) { appraisal.uuid }
        let!(:vehicle_condition) { create(:vehicle_condition, customer_vehicle: customer_vehicle) }

        let(:condition) do
          {
            is_clean: true,
            is_wet: false,
            is_road_tested: true,
            has_signs_of_repair: true,
            repair_details: "Minor scratch repaired",
            additional_notes: "Updated condition after inspection"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'code')).to eq(200)
          expect(data.dig('status', 'message')).to eq('Vehicle condition updated successfully')
          expect(data.dig('data', 'appraisal', 'vehicle', 'vehicle_condition')).to be_present
        end
      end

      response "400", "Invalid input" do
        let(:appraisal_uuid) { appraisal.uuid }
        let(:condition) { { invalid_field: 'value' } }
        run_test!
      end

      response "401", "Unauthorized" do
        let(:appraisal_uuid) { appraisal.uuid }
        let(:condition) { { is_clean: true } }
        let(:Authorization) { 'invalid_token' }
        run_test!
      end

      response "404", "Appraisal not found in dealership" do
        let(:appraisal) { create(:appraisal, dealership: create(:dealership), customer: customer, sales_person: sales_person) }
        let(:appraisal_uuid) { appraisal.uuid }
        let(:condition) { { is_clean: true } }
        run_test!
      end

      response "404", "Appraisal not found" do
        let(:appraisal_uuid) { 'non-existent-uuid' }
        let(:condition) { { is_clean: true } }
        run_test!
      end

      response "201", "Vehicle condition with photos created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Vehicle condition created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 is_clean: { type: :boolean, example: true },
                                 is_wet: { type: :boolean, example: false },
                                 is_road_tested: { type: :boolean, example: true },
                                 has_signs_of_repair: { type: :boolean, example: false },
                                 repair_details: { type: :string, example: "No previous repairs detected" },
                                 additional_notes: { type: :string, example: "Vehicle is in excellent condition" }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:photo1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:photo2) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:condition) do
          {
            is_clean: true,
            is_wet: false,
            is_road_tested: true,
            has_signs_of_repair: false,
            repair_details: "No previous repairs detected",
            additional_notes: "Vehicle is in excellent condition",
            photos: [ photo1, photo2 ]
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Vehicle condition created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "vehicle_condition")).to be_present

          vehicle_condition = json.dig("data", "appraisal", "vehicle", "vehicle_condition")
          expect(vehicle_condition["is_clean"]).to eq(true)
          expect(vehicle_condition["is_wet"]).to eq(false)
          expect(vehicle_condition["is_road_tested"]).to eq(true)
          expect(vehicle_condition["has_signs_of_repair"]).to eq(false)
          expect(vehicle_condition["repair_details"]).to eq("No previous repairs detected")
          expect(vehicle_condition["additional_notes"]).to eq("Vehicle is in excellent condition")

          # Verify photos were attached
          vehicle_condition_record = Vehicle::VehicleCondition.joins(:customer_vehicle).where(customer_vehicles: { appraisal_id: appraisal.id }).first
          expect(vehicle_condition_record.photos.attached?).to be true
          expect(vehicle_condition_record.photos.count).to eq(2)
        end
      end
    end
  end

  describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/vehicle-condition" do
    subject { put "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal_uuid}/vehicle-condition", params: payload, headers: headers }

    let(:dealership_uuid) { dealership.uuid }
    let(:appraisal_uuid) { appraisal.uuid }

    context "when creating a new vehicle condition" do
      let(:payload) do
        {
          condition: {
            is_clean: true,
            is_wet: false,
            is_road_tested: true,
            has_signs_of_repair: false,
            repair_details: "No previous repairs detected",
            additional_notes: "Vehicle is in excellent condition"
          }
        }
      end

      it "creates a new vehicle condition" do
        expect { subject }.to change { Vehicle::VehicleCondition.count }.by(1)
        expect(response).to have_http_status(:created)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(201)
        expect(data.dig('status', 'message')).to eq('Vehicle condition created successfully')

        vehicle_condition = customer_vehicle.reload.vehicle_condition
        expect(vehicle_condition).to be_present
        expect(vehicle_condition.is_clean).to be true
        expect(vehicle_condition.is_wet).to be false
        expect(vehicle_condition.is_road_tested).to be true
        expect(vehicle_condition.has_signs_of_repair).to be false
        expect(vehicle_condition.repair_details).to eq("No previous repairs detected")
        expect(vehicle_condition.additional_notes).to eq("Vehicle is in excellent condition")
      end
    end

    context "when updating an existing vehicle condition" do
      let!(:vehicle_condition) { create(:vehicle_condition, customer_vehicle: customer_vehicle) }

      let(:payload) do
        {
          condition: {
            is_clean: false,
            is_wet: true,
            is_road_tested: false,
            has_signs_of_repair: true,
            repair_details: "Minor scratch repaired",
            additional_notes: "Updated condition after inspection"
          }
        }
      end

      it "updates the existing vehicle condition" do
        expect { subject }.not_to change { Vehicle::VehicleCondition.count }
        expect(response).to have_http_status(:ok)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(200)
        expect(data.dig('status', 'message')).to eq('Vehicle condition updated successfully')

        vehicle_condition.reload
        expect(vehicle_condition.is_clean).to be false
        expect(vehicle_condition.is_wet).to be true
        expect(vehicle_condition.is_road_tested).to be false
        expect(vehicle_condition.has_signs_of_repair).to be true
        expect(vehicle_condition.repair_details).to eq("Minor scratch repaired")
        expect(vehicle_condition.additional_notes).to eq("Updated condition after inspection")
      end
    end

    context "with invalid parameters" do
      let(:payload) { { condition: { invalid_field: 'value' } } }

      it "returns an error" do
        subject
        expect(response).to have_http_status(:bad_request)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(400)
        expect(data.dig('status', 'message')).to eq('param is missing or the value is empty or invalid: condition')
      end
    end

    context "when appraisal is archived" do
      let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
      let(:payload) { { condition: { is_clean: true } } }

      it "returns an error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(422)
        expect(data.dig('status', 'message')).to eq('Cannot modify archived appraisal')
      end
    end

    context "when appraisal is deleted" do
      let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :deleted) }
      let(:payload) { { condition: { is_clean: true } } }

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(404)
        expect(data.dig('status', 'message')).to eq('Appraisal not found or does not belong to this dealership')
      end
    end

    context "when appraisal does not exist" do
      let(:appraisal_uuid) { 'non-existent-uuid' }
      let(:payload) { { condition: { is_clean: true } } }

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(404)
        expect(data.dig('status', 'message')).to eq('Appraisal not found or does not belong to this dealership')
      end
    end

    context "when user does not have access to the dealership" do
      let(:appraisal) { create(:appraisal, dealership: create(:dealership), customer: customer, sales_person: sales_person) }
      let(:payload) { { condition: { is_clean: true } } }

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)

        data = response.parsed_body
        expect(data.dig('status', 'code')).to eq(404)
        expect(data.dig('status', 'message')).to eq('Appraisal not found or does not belong to this dealership')
      end
    end
  end
end
