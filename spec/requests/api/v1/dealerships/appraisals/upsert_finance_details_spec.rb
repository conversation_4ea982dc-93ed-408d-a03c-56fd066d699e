# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/finance-details", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/finance-details" do
    put "Upsert finance details for appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[finance],
        properties: {
          finance: {
            type: :object,
            properties: {
              is_financed: {
                type: :string,
                enum: [ 'financed', 'not_financed', 'unknown' ],
                description: "Whether the vehicle is currently financed. Allowed values: 'financed', 'not_financed', 'unknown'",
                example: 'financed'
              },
              finance_company: {
                type: :string,
                description: "Name of the finance company",
                example: "ABC Finance"
              },
              current_repayment_amount: {
                type: :number,
                format: :float,
                description: "Current monthly repayment amount",
                example: 450.00
              },
              terms_months: {
                type: :integer,
                description: "Finance term in months",
                example: 60
              },
              interest_rate: {
                type: :number,
                format: :float,
                description: "Interest rate percentage",
                example: 5.5
              },
              next_due_date: {
                type: :string,
                format: :date,
                description: "Next payment due date",
                example: "2024-02-15"
              },
              has_clear_title: {
                type: :boolean,
                description: "Whether the vehicle has a clear title",
                example: false
              },
              payout_amount: {
                type: :number,
                format: :float,
                description: "Payout amount to clear the finance",
                example: 25000.00
              }
            }
          }
        }
      }

      response "201", "Finance details created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Finance details created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             finance_details: {
                               type: :object,
                               properties: {
                                 is_financed: { type: :string, enum: [ 'financed', 'not_financed', 'unknown' ], example: 'financed' },
                                 finance_company: { type: :string, example: "ABC Finance" },
                                 current_repayment_amount: { type: :string, example: "450.00" },
                                 terms_months: { type: :integer, example: 60 },
                                                                    interest_rate: { type: :string, example: "5.5" },
                                 next_due_date: { type: :string, format: :date, example: "2024-02-15" },
                                 has_clear_title: { type: :boolean, example: false },
                                 payout_amount: { type: :string, example: "25000.00" }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "ABC Finance",
            current_repayment_amount: 450.00,
            terms_months: 60,
            interest_rate: 5.5,
            next_due_date: "2024-02-15",
            has_clear_title: false,
            payout_amount: 25000.00
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Finance details created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          finance_details = json.dig("data", "appraisal", "vehicle", "finance_details")
          expect(finance_details).to be_present
          expect(finance_details["is_financed"]).to eq('financed')
          expect(finance_details["finance_company"]).to eq("ABC Finance")
          expect(finance_details["current_repayment_amount"]).to eq("450.0")
          expect(finance_details["terms_months"]).to eq(60)
          expect(finance_details["interest_rate"]).to eq("5.5")
          expect(finance_details["next_due_date"]).to eq("2024-02-15")
          expect(finance_details["has_clear_title"]).to eq(false)
          expect(finance_details["payout_amount"]).to eq("25000.0")
        end
      end

      response "200", "Finance details updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Finance details updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             finance_details: {
                               type: :object,
                               properties: {
                                 is_financed: { type: :string, enum: [ 'financed', 'not_financed', 'unknown' ], example: 'financed' },
                                 finance_company: { type: :string, example: "XYZ Finance" },
                                 current_repayment_amount: { type: :string, example: "500.00" },
                                 terms_months: { type: :integer, example: 48 },
                                 interest_rate: { type: :string, example: "4.5" },
                                 next_due_date: { type: :string, format: :date, example: "2024-03-15" },
                                 has_clear_title: { type: :boolean, example: true },
                                                                    payout_amount: { type: :string, example: "22000.00" }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_finance) { create(:finance_details, customer_vehicle: customer_vehicle, is_financed: :financed, finance_company: "ABC Finance", current_repayment_amount: 450.00, next_due_date: "2024-02-15") }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "XYZ Finance",
            current_repayment_amount: 500.00,
            terms_months: 48,
            interest_rate: 4.5,
            next_due_date: "2024-03-15",
            has_clear_title: true,
            payout_amount: 22000.00
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Finance details updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          finance_details = json.dig("data", "appraisal", "vehicle", "finance_details")
          expect(finance_details).to be_present
          expect(finance_details["is_financed"]).to eq('financed')
          expect(finance_details["finance_company"]).to eq("XYZ Finance")
          expect(finance_details["current_repayment_amount"]).to eq("500.0")
          expect(finance_details["terms_months"]).to eq(48)
          expect(finance_details["interest_rate"]).to eq("4.5")
          expect(finance_details["next_due_date"]).to eq("2024-03-15")
          expect(finance_details["has_clear_title"]).to eq(true)
          expect(finance_details["payout_amount"]).to eq("22000.0")
        end
      end

      response "422", "Validation failed" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Current repayment amount must be greater than or equal to 0" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            current_repayment_amount: -100.00
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
        end
      end

      response "422", "Invalid is_financed value" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Invalid value is not included in the list" }
                   }
                 },
                 errors: {
                   type: :array,
                   items: {
                     type: :object,
                     properties: {
                       message: { type: :string, example: "Invalid value is not included in the list" }
                     }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { finance: {
            is_financed: 'invalid_value',
            finance_company: "ABC Finance"
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
          expect(json["errors"]).to be_present
          expect(json["errors"].first["message"]).to include("is not included in the list")
        end
      end

      response "422", "No vehicle found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { appraisal_without_vehicle.uuid }
        let(:appraisal_without_vehicle) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "ABC Finance"
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "ABC Finance"
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "ABC Finance"
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { finance: {
            is_financed: 'financed',
            finance_company: "ABC Finance"
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end
    end
  end
end
