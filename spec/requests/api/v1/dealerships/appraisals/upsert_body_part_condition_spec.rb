# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/body-part-condition", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }
  let!(:vehicle_condition) { create(:vehicle_condition, customer_vehicle: customer_vehicle) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/body-part-condition" do
    put "Upsert body part condition for appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :body_part_condition, in: :formData, schema: {
        type: :object,
        required: %w[part_name condition],
        properties: {
          part_name: {
            type: :string,
            enum: %w[
              front_bumper front_fender_skirt front_panel left_front_headlamp right_front_headlamp bonnet
              left_front_fender right_front_fender left_front_tyre left_front_wheel right_front_tyre
              right_front_wheel front_windshield left_front_door left_front_window left_running_board
              left_rear_window left_rear_door left_rear_fender left_rear_tyre left_rear_wheel
              right_front_door right_front_window right_running_board right_rear_window right_rear_door
              right_rear_fender right_rear_tyre right_rear_wheel rear_windshield boot left_rear_headlamp
              right_rear_headlamp rear_grill roof rear_fender_skirt rear_bumper
            ],
            description: "Body part name",
            example: "front_bumper"
          },
          condition: {
            type: :string,
            enum: %w[okay scratch chip dent hail damaged acceptable unacceptable],
            description: "Body part condition",
            example: "scratch"
          },
          description: {
            type: :string,
            description: "Optional description of the condition",
            example: "Minor scratch on the front bumper"
          },
          photos: {
            type: :array,
            items: { type: :string, format: :binary },
            description: "Photos for this body part"
          }
        }
      }

      response "201", "Body part condition created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Body part condition created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 body_part_conditions: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       part_name: { type: :string, example: "front_bumper" },
                                       condition: { type: :string, example: "scratch" },
                                       description: { type: :string, example: "Minor scratch on the front bumper" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:body_part_condition) do
          {
            part_name: "front_bumper",
            condition: "scratch",
            description: "Minor scratch on the front bumper"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Body part condition created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")).to be_present

          body_part_conditions = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")
          front_bumper = body_part_conditions.find { |bpc| bpc["part_name"] == "front_bumper" }
          expect(front_bumper).to be_present
          expect(front_bumper["id"]).to be_present
          expect(front_bumper["condition"]).to eq("scratch")
          expect(front_bumper["description"]).to eq("Minor scratch on the front bumper")
        end
      end

      response "200", "Body part condition updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Body part condition updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 body_part_conditions: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       part_name: { type: :string, example: "bonnet" },
                                       condition: { type: :string, example: "dent" },
                                       description: { type: :string, example: "Updated condition" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]


        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_body_part) { create(:body_part_condition, vehicle_condition: vehicle_condition, part_name: :bonnet, condition: :okay) }
        let(:body_part_condition) do
          {
            part_name: "bonnet",
            condition: "dent",
            description: "Updated condition"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Body part condition updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          body_part_conditions = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")
          bonnet = body_part_conditions.find { |bpc| bpc["part_name"] == "bonnet" }
          expect(bonnet).to be_present
          expect(bonnet["id"]).to eq(existing_body_part.id)
          expect(bonnet["condition"]).to eq("dent")
          expect(bonnet["description"]).to eq("Updated condition")
        end
      end

      response "201", "Body part condition created successfully with new part names" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Body part condition created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid },
                         vehicle: {
                           type: :object,
                           properties: {
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 body_part_conditions: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       part_name: { type: :string, example: "roof" },
                                       condition: { type: :string, example: "okay" },
                                       description: { type: :string, example: "Roof in good condition" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:body_part_condition) do
          {
            part_name: "roof",
            condition: "okay",
            description: "Roof in good condition"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Body part condition created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          body_part_conditions = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")
          roof = body_part_conditions.find { |bpc| bpc["part_name"] == "roof" }
          expect(roof).to be_present
          expect(roof["condition"]).to eq("okay")
          expect(roof["description"]).to eq("Roof in good condition")
        end
      end

      response "422", "No vehicle condition found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle condition found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal_without_condition.uuid }
        let(:appraisal_without_condition) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let!(:vehicle_without_condition) { create(:customer_vehicle, appraisal: appraisal_without_condition, customer: customer, dealership: dealership) }
        let(:body_part_condition) do
          {
            part_name: "front_bumper",
            condition: "scratch"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle condition found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let!(:archived_condition) { create(:vehicle_condition, customer_vehicle: archived_vehicle) }
        let(:body_part_condition) do
          {
            part_name: "front_bumper",
            condition: "scratch"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:body_part_condition) do
          {
            part_name: "front_bumper",
            condition: "scratch"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:body_part_condition) do
          {
            part_name: "front_bumper",
            condition: "scratch"
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response "201", "Body part condition with photos created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Body part condition created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 body_part_conditions: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       part_name: { type: :string, example: "boot" },
                                       condition: { type: :string, example: "scratch" },
                                       description: { type: :string, example: "major" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:photo1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:photo2) { fixture_file_upload(Rails.root.join("spec/fixtures/files/options_image1.jpg"), 'image/jpeg') }
        let(:body_part_condition) do
          {
            part_name: "boot",
            condition: "scratch",
            description: "major",
            photos: [ photo1, photo2 ]
          }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Body part condition created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")).to be_present

          body_part_conditions = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "body_part_conditions")
          boot = body_part_conditions.find { |bpc| bpc["part_name"] == "boot" }
          expect(boot).to be_present
          expect(boot["id"]).to be_present
          expect(boot["condition"]).to eq("scratch")
          expect(boot["description"]).to eq("major")

          # Verify photos were attached
          body_part_condition_record = Vehicle::BodyPartCondition.find(boot["id"])
          expect(body_part_condition_record.photos.attached?).to be true
          expect(body_part_condition_record.photos.count).to eq(2)
        end
      end
    end
  end
end
