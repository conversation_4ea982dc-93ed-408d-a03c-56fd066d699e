# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/send-for-offers", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let(:appraisal_uuid) { appraisal.uuid }
  let(:existing_valuer) { create(:appraisal_valuer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/send-for-offers" do
    post "Send offers for an appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"
      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[offers],
        properties: {
          offers: {
            type: :array,
            items: {
              type: :object,
              properties: {
                offer_price: { type: :number, example: 25000.00 },
                offer_notes: { type: :string, example: "Good condition vehicle" },
                is_internal: { type: :boolean, example: false },
                appraisal_valuer_uuid: { type: :string, example: "1" },
                is_verbal: { type: :boolean, example: true },
                valuer_business_name: { type: :string, example: "ABC Valuers" },
                valuer_email: { type: :string, example: "<EMAIL>" },
                valuer_first_name: { type: :string, example: "John" },
                valuer_last_name: { type: :string, example: "Doe" },
                valuer_mobile_number: { type: :string, example: "+***********" },
                add_valuer: { type: :boolean, example: false }
              }
            }
          }
        }
      }

      response "200", "Offers sent successfully" do
        let(:payload) do
          {
            offers: [
              {
                offer_price: 25000.00,
                offer_notes: "Internal offer",
                is_internal: true,
                appraisal_valuer_uuid: nil,
                is_verbal: false,
                valuer_business_name: nil,
                valuer_email: nil,
                valuer_first_name: nil,
                valuer_last_name: nil,
                valuer_mobile_number: nil,
                add_valuer: false
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(200)
          expect(data['status']['message']).to eq('Offers sent successfully')
          expect(data['data']['created_offers_count']).to eq(1)
          
          # Verify the offer was created
          offer = AppraisalOffer.last
          expect(offer.appraisal).to eq(appraisal)
          expect(offer.offer_price).to eq(25000.00)
          expect(offer.is_internal_offer).to be true
          expect(offer.is_verbal_offer).to be false
          expect(offer.verbal_offer_by_id).to eq(user.id)
        end
      end

      response "422", "Validation error" do
        let(:payload) do
          {
            offers: [
              {
                offer_price: nil,
                offer_notes: "Missing price",
                is_internal: false,
                appraisal_valuer_uuid: nil,
                is_verbal: true,
                valuer_business_name: "ABC Valuers",
                valuer_email: "<EMAIL>",
                valuer_first_name: "John",
                valuer_last_name: "Doe",
                valuer_mobile_number: "+***********",
                add_valuer: false
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(422)
          expect(data['status']['message']).to include('offer_price is mandatory for verbal offers')
        end
      end

      response "422", "Duplicate valuer email error" do
        let(:payload) do
          {
            offers: [
              {
                offer_price: 25000.00,
                offer_notes: "New valuer offer",
                is_internal: false,
                appraisal_valuer_uuid: nil,
                is_verbal: true,
                valuer_business_name: "ABC Valuers",
                valuer_email: existing_valuer.email,
                valuer_first_name: "John",
                valuer_last_name: "Doe",
                valuer_mobile_number: "+***********",
                add_valuer: true
              }
            ]
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(422)
          expect(data['status']['message']).to include('already exists for this dealership')
        end
      end
    end
  end

  describe "POST /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/send-for-offers" do
    context "when creating internal offer" do
      it "creates an internal offer successfully" do
        payload = {
          offers: [
            {
              offer_price: 30000.00,
              offer_notes: "Internal assessment",
              is_internal: true,
              appraisal_valuer_uuid: nil,
              is_verbal: false,
              valuer_business_name: nil,
              valuer_email: nil,
              valuer_first_name: nil,
              valuer_last_name: nil,
              valuer_mobile_number: nil,
              add_valuer: false
            }
          ]
        }

        post "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal_uuid}/send-for-offers",
             params: payload.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        offer = AppraisalOffer.last
        expect(offer.appraisal).to eq(appraisal)
        expect(offer.offer_price).to eq(30000.00)
        expect(offer.is_internal_offer).to be true
        expect(offer.is_verbal_offer).to be false
        expect(offer.verbal_offer_by_id).to eq(user.id)
        expect(offer.offer_date).to eq(Date.current)
      end
    end

    context "when creating offer with existing valuer" do
      it "creates offer with existing valuer successfully" do
        payload = {
          offers: [
            {
              offer_price: 28000.00,
              offer_notes: "External valuation",
              is_internal: false,
              appraisal_valuer_uuid: existing_valuer.id.to_s,
              is_verbal: true,
              valuer_business_name: nil,
              valuer_email: nil,
              valuer_first_name: nil,
              valuer_last_name: nil,
              valuer_mobile_number: nil,
              add_valuer: false
            }
          ]
        }

        post "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal_uuid}/send-for-offers",
             params: payload.to_json,
             headers: headers

        expect(response).to have_http_status(:ok)
        
        offer = AppraisalOffer.last
        expect(offer.appraisal).to eq(appraisal)
        expect(offer.appraisal_valuer).to eq(existing_valuer)
        expect(offer.offer_price).to eq(28000.00)
        expect(offer.is_verbal_offer).to be true
        expect(offer.verbal_offer_by_id).to eq(user.id)
      end
    end

    context "when creating new valuer and offer" do
      it "creates new valuer and offer successfully" do
        payload = {
          offers: [
            {
              offer_price: 26000.00,
              offer_notes: "New valuer assessment",
              is_internal: false,
              appraisal_valuer_uuid: nil,
              is_verbal: true,
              valuer_business_name: "XYZ Valuers",
              valuer_email: "<EMAIL>",
              valuer_first_name: "Jane",
              valuer_last_name: "Smith",
              valuer_mobile_number: "+***********",
              add_valuer: true
            }
          ]
        }

        expect {
          post "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal_uuid}/send-for-offers",
               params: payload.to_json,
               headers: headers
        }.to change(AppraisalValuer, :count).by(1)
         .and change(AppraisalOffer, :count).by(1)

        expect(response).to have_http_status(:ok)
        
        new_valuer = AppraisalValuer.last
        expect(new_valuer.business_name).to eq("XYZ Valuers")
        expect(new_valuer.email).to eq("<EMAIL>")
        expect(new_valuer.dealership).to eq(dealership)
        
        offer = AppraisalOffer.last
        expect(offer.appraisal_valuer).to eq(new_valuer)
        expect(offer.offer_price).to eq(26000.00)
        expect(offer.is_verbal_offer).to be true
      end
    end

    context "when creating external valuer offer without adding valuer" do
      it "creates offer with valuer details but no valuer record" do
        payload = {
          offers: [
            {
              offer_price: 24000.00,
              offer_notes: "External assessment",
              is_internal: false,
              appraisal_valuer_uuid: nil,
              is_verbal: true,
              valuer_business_name: "External Valuers",
              valuer_email: "<EMAIL>",
              valuer_first_name: "Bob",
              valuer_last_name: "Johnson",
              valuer_mobile_number: "+***********",
              add_valuer: false
            }
          ]
        }

        expect {
          post "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal_uuid}/send-for-offers",
               params: payload.to_json,
               headers: headers
        }.to change(AppraisalOffer, :count).by(1)
         .and change(AppraisalValuer, :count).by(0)

        expect(response).to have_http_status(:ok)
        
        offer = AppraisalOffer.last
        expect(offer.appraisal_valuer).to be_nil
        expect(offer.valuer_business_name).to eq("External Valuers")
        expect(offer.valuer_email).to eq("<EMAIL>")
        expect(offer.valuer_first_name).to eq("Bob")
        expect(offer.valuer_last_name).to eq("Johnson")
        expect(offer.valuer_mobile_number).to eq("+***********")
        expect(offer.is_verbal_offer).to be true
      end
    end
  end
end
