# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::AppraisalsController", type: :request do
  include_context "appraisal_api_shared_context"

  let!(:customer1) { create(:customer, dealership: dealership) }
  let!(:customer2) { create(:customer, dealership: dealership) }
  let!(:customer3) { create(:customer, dealership: dealership) }
  let!(:customer4) { create(:customer, dealership: dealership) }

  # Create appraisals for dashboard data
  let!(:completed_appraisal) do
    create(:appraisal, :with_vehicle,
           dealership: dealership,
           customer: customer1,
           sales_person: sales_person,
           status: :complete)
  end

  let!(:pending_appraisal) do
    create(:appraisal, :with_vehicle,
           dealership: dealership,
           customer: customer2,
           sales_person: sales_person,
           status: :incomplete)
  end

  let!(:awarded_appraisal) do
    create(:appraisal, :with_vehicle,
           dealership: dealership,
           customer: customer3,
           sales_person: sales_person,
           status: :awarded)
  end

  let!(:archived_appraisal) do
    create(:appraisal, :with_vehicle,
           dealership: dealership,
           customer: customer3,
           sales_person: sales_person,
           status: :archived)
  end

  let!(:deleted_appraisal) do
    create(:appraisal, :with_vehicle,
           dealership: dealership,
           customer: customer3,
           sales_person: sales_person,
           status: :deleted)
  end

  let!(:favourite_appraisal) do
    create(:favourite_appraisal, appraisal: awarded_appraisal, user: user)
  end

  path '/api/v1/dealerships/{dealership_uuid}/appraisals/dashboard' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    get('Get dashboard data for appraisals') do
      tags 'Appraisals'
      description 'Retrieves dashboard statistics for appraisals including total count, completed, pending, and user favourites'
      operationId 'getAppraisalsDashboard'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Dashboard data retrieved successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     total_appraisals: { type: :integer, example: 10, description: 'Total number of appraisals' },
                     completed: { type: :integer, example: 3, description: 'Number of completed appraisals' },
                     pending: { type: :integer, example: 5, description: 'Number of pending appraisals' },
                     awarded: { type: :integer, example: 1, description: 'Number of awarded appraisals' },
                     archived: { type: :integer, example: 1, description: 'Number of archived appraisals' },
                     my_favourites: { type: :integer, example: 2, description: 'Number of appraisals favourited by current user' }
                   },
                   required: [ 'total_appraisals', 'completed', 'pending', 'my_favourites' ]
                 }
               },
               required: [ 'status', 'data' ]

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Dashboard data retrieved successfully")
          expect(json.dig("data", "total_appraisals")).to eq(4)
          expect(json.dig("data", "completed")).to eq(1)
          expect(json.dig("data", "pending")).to eq(1)
          expect(json.dig("data", "awarded")).to eq(1)
          expect(json.dig("data", "archived")).to eq(1)
          expect(json.dig("data", "my_favourites")).to eq(1)
        end
      end

      response(401, 'unauthorized') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing or invalid authorization token' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:Authorization) { "Bearer invalid-token" }
        let(:'Device-ID') { 'some-device-id' }

        run_test!
      end
    end
  end

  # Regular RSpec request specs for dashboard method
  describe "GET /api/v1/dealerships/:dealership_uuid/appraisals/dashboard" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/appraisals/dashboard" }

    context "when authenticated" do
      it "returns dashboard data successfully" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Dashboard data retrieved successfully")
        expect(json["data"]).to include(
          "total_appraisals",
          "completed",
          "pending",
          "awarded",
          "archived",
          "my_favourites"
        )
      end

      it "returns correct counts for each metric" do
        get url, headers: headers

        json = response.parsed_body
        data = json["data"]

        expect(data["total_appraisals"]).to eq(4)
        expect(data["completed"]).to eq(1)
        expect(data["pending"]).to eq(1)
        expect(data["awarded"]).to eq(1)
        expect(data["archived"]).to eq(1)
        expect(data["my_favourites"]).to eq(1)
      end

      it "excludes deleted appraisals from total count" do
        # Create a deleted appraisal
        create(:appraisal, :with_vehicle,
               dealership: dealership,
               customer: create(:customer, dealership: dealership),
               sales_person: sales_person,
               status: :deleted)

        get url, headers: headers

        json = response.parsed_body
        expect(json["data"]["total_appraisals"]).to eq(4) # Should still be 4, not 5
      end

      it "correctly counts user's favourites" do
        # Create another favourite for the same user
        another_appraisal = create(:appraisal, :with_vehicle,
                                 dealership: dealership,
                                 customer: create(:customer, dealership: dealership),
                                 sales_person: sales_person)
        create(:favourite_appraisal, appraisal: another_appraisal, user: user)

        get url, headers: headers

        json = response.parsed_body
        expect(json["data"]["my_favourites"]).to eq(2)
      end
    end

    context "when not authenticated" do
      it "returns unauthorized error" do
        get url

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when dealership not found" do
      let(:url) { "/api/v1/dealerships/invalid-uuid/appraisals/dashboard" }

      it "returns not found error" do
        get url, headers: headers

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
