FactoryBot.define do
  factory :appraisal_valuer do
    association :dealership
    sequence(:business_name) { |n| "#{Faker::Company.name} #{n}" }
    sequence(:email) { |n| "valuer#{n}@example.com" }
    first_name { Faker::Name.first_name }
    last_name { Faker::Name.last_name }
    mobile_number { "+***********" }
    status { :active }

    trait :inactive do
      status { :inactive }
    end

    trait :deleted do
      status { :deleted }
    end

    trait :with_specific_email do
      email { "<EMAIL>" }
    end
  end
end
